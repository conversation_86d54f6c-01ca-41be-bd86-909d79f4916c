from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from security_service.app.models.role import Role
from security_service.dependencies.db import SQLALCHEMY_DATABASE_URL


# Create database connection
engine = create_engine(SQLALCHEMY_DATABASE_URL)
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
db = SessionLocal()


def create_roles():
    """Create roles if they don't exist"""
    # Define roles to create
    roles = [
        "superadmin",
        "admin",
        # "staff",
        # "student",
        "security"
    ]
    
    try:
        # Get existing roles
        existing_roles = {
            role.name: role 
            for role in db.query(Role).all()
        }
        
        # Create new roles only if they don't exist
        new_roles = 0
        for role_name in roles:
            if role_name not in existing_roles:
                role = Role(name=role_name)
                db.add(role)
                new_roles += 1
        
        db.commit()
        print(f"Successfully added {new_roles} new roles")
        
    except Exception as e:
        print(f"Error creating roles: {str(e)}")
        db.rollback()
    finally:
        db.close()


if __name__ == "__main__":
    create_roles() 