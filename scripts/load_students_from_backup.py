import json
import os
from sqlalchemy import create_engine, and_
from sqlalchemy.orm import sessionmaker
import shutil
from demographic_service.app.models import Person, Student
from demographic_service.dependencies.db import SQLALCHEMY_DATABASE_URL
from security_service.dependencies.db import SQLALCHEMY_DATABASE_URL as SECURITY_SQLALCHEMY_DATABASE_URL
from security_service.app.models.person_status import PersonStatus  # Import your model

# Determine the path to the fixtures JSON
base_dir = os.path.dirname(os.path.abspath(__file__))
fixtures_path = os.path.join(base_dir, "files", "backup", "all_fixtures.json")

# Load the JSON fixtures
with open(fixtures_path, 'r', encoding='utf-8') as f:
    data = json.load(f)

# Set up the database session
engine = create_engine(SQLALCHEMY_DATABASE_URL)
SessionLocal = sessionmaker(bind=engine, autoflush=False, autocommit=False)

# Set up the security database session
security_engine = create_engine(SECURITY_SQLALCHEMY_DATABASE_URL)
SecuritySessionLocal = sessionmaker(bind=security_engine, autoflush=False, autocommit=False)

def safe_strip(val):
    """Strip strings and normalize None/empty values."""
    if val is None:
        return None
    if isinstance(val, str):
        v = val.strip()
        return v if v else None
    return str(val).strip()


def load_students():
    db = SessionLocal()
    security_db = SecuritySessionLocal()
    try:
        for row in data:
            if row.get('model') != 'api.person':
                continue
            fields = row.get('fields', {})

            # Extract person info
            first = safe_strip(fields.get('name'))
            middle = safe_strip(fields.get('middle_name'))
            last = safe_strip(fields.get('surname'))
            dob = safe_strip(fields.get('date_of_birth'))  # YYYY-MM-DD or None
            gender = safe_strip(fields.get('gender'))
            email = safe_strip(fields.get('email'))
            phone = safe_strip(fields.get('phone_number'))
            nationality = safe_strip(fields.get('nationality'))
            profile_picture = fields.get('profile_pic', None)

            # Extract student info
            grad_year = fields.get('graduation_year')
            dept = safe_strip(fields.get('major'))

            # 1) Find or create Person
            filters = [
                Person.name == first if first else Person.name.is_(None),
                Person.surname == last if last else Person.surname.is_(None),
                Person.middle_name == middle if middle else Person.middle_name.is_(None),
                Person.email == email if email else Person.email.is_(None),
            ]
            person = db.query(Person).filter(and_(*filters)).first()
            if not person:
                print(f"Creating person: {first} {middle or ''} {last or ''}")
                print(profile_picture)
                if profile_picture:
                    parent = (
                        str(first or ".") + "_" +
                        str(middle or ".") + "_" +
                        str(last or ".")
                    )
                    save_to = os.path.join("uploads/profile_picture", parent + ".jpg")
                    from_ = os.path.join("/app/scripts/files/backup/files/uploads/profile_pictures/", os.path.basename(profile_picture))
                    profile_picture = None
                    print(os.curdir)
                    try:
                        os.makedirs(os.path.dirname(save_to), exist_ok=True)  # Ensure the directory exists
                        shutil.copyfile(from_, save_to)  # Copy the file
                        profile_picture = parent + ".jpg" # Set the file path for the database
                        print(f"Copied profile picture to {save_to}")
                    except FileNotFoundError:
                        print(f"Profile picture not found: {from_}")
                    except Exception as e:
                        print(f"Error copying profile picture: {e}")
                        
                if phone:
                    phone = phone.replace(" ", "")
                if email:
                    email = email.replace(" ", "")
                if gender:
                    gender = "Male"
                person = Person(
                    name=first,
                    surname=last,
                    middle_name=middle,
                    date_of_birth=dob,
                    gender=gender,
                    email=email,
                    phone_number=phone,
                    nationality=nationality,
                    profile_picture=profile_picture,
                )
                db.add(person)
                db.flush()  # populate person.id

            # 2) Find or create Student linked to Person
            student = db.query(Student).filter(Student.person_id == person.id).first()
            if not student:
                print(f"Creating student for person_id={person.id}")
                print(grad_year, dept)
                if dept is None or dept == "BSc in Computer Science":
                    dept = "Computer Science"
                else:
                    dept = "Communication and Media"
                if grad_year is None:
                    grad_year = 0
                student = Student(
                    graduation_year=grad_year,
                    department=dept,
                    person_id=person.id,
                )
                db.add(student)

            # 3) Create PersonStatus in security DB if not exists
            person_status = security_db.query(PersonStatus).filter(PersonStatus.person_id == person.id).first()
            if not person_status:
                person_status = PersonStatus(person_id=person.id, on_campus=True)
                security_db.add(person_status)
                print(f"Created PersonStatus for person_id={person.id}")

        # Commit all changes
        db.commit()
        security_db.commit()
        print("Student data loaded successfully.")
    except Exception as e:
        db.rollback()
        security_db.rollback()
        print(f"Error loading students: {e}")
    finally:
        db.close()
        security_db.close()


if __name__ == "__main__":
    load_students()
