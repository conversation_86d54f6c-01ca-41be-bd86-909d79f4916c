import os
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from demographic_service.app.models import Person
from security_service.app.models.record import Record
from security_service.app.models.security_person import SecurityPerson
from security_service.app.models.role import Role
from security_service.app.models.permission import Permission
from security_service.app.models.api import Api
from demographic_service.dependencies.db import Base as DemographicBase
from security_service.dependencies.db import Base as SecurityBase
from auth_service.app.models.User import User
from auth_service.dependencies.auth import get_password_hash
from demographic_service.dependencies.db import SQLALCHEMY_DATABASE_URL as DEMOGRAPHIC_DB_URL
from security_service.dependencies.db import SQLALCHEMY_DATABASE_URL as SECURITY_DB_URL
from auth_service.dependencies.db import SQLALCHEMY_DATABASE_URL as AUTH_DB_URL

# Create database engines
demographic_engine = create_engine(DEMOGRAPHIC_DB_URL)
security_engine = create_engine(SECURITY_DB_URL)
auth_engine = create_engine(AUTH_DB_URL)

# Create session makers
DemographicSession = sessionmaker(autocommit=False, autoflush=False, bind=demographic_engine)
SecuritySession = sessionmaker(autocommit=False, autoflush=False, bind=security_engine)
AuthSession = sessionmaker(autocommit=False, autoflush=False, bind=auth_engine)

def init_first_user():
    demographic_session = DemographicSession()
    security_session = SecuritySession()
    auth_session = AuthSession()

    try:
        # Step 1: Check if the first Person exists
        person = demographic_session.query(Person).filter(Person.email == "<EMAIL>").first()
        if not person:
            print("Creating first Person...")
            person = Person(
                name="Admin",
                surname="User",
                middle_name="Super",
                email="<EMAIL>",
                phone_number="+123456789",
                campus="Naryn",
                nationality="Testland"
            )
            demographic_session.add(person)
            demographic_session.flush()  # Get the person ID
        else:
            print("Person already exists.")

        # Step 2: Check if the Role exists
        role = security_session.query(Role).filter(Role.name == "Super Admin").first()
        if not role:
            print("Creating Super Admin Role...")
            role = Role(
                name="superadmin"
            )
            security_session.add(role)
            security_session.flush()  # Get the role ID
        else:
            print("Role already exists.")

        # Step 3: Assign all APIs to the Role if not already assigned
        print("Assigning permissions to the Role...")
        apis = security_session.query(Api).all()
        for api in apis:
            permission_exists = security_session.query(Permission).filter(
                Permission.api_id == api.id,
                Permission.role_id == role.id
            ).first()
            if not permission_exists:
                permission = Permission(
                    api_id=api.id,
                    role_id=role.id,
                    is_active=True
                )
                security_session.add(permission)

        # Step 4: Check if the SecurityPerson exists
        security_person = security_session.query(SecurityPerson).filter(SecurityPerson.person_id == person.id).first()
        if not security_person:
            print("Creating SecurityPerson...")
            security_person = SecurityPerson(
                person_id=person.id,
                role=role.name
            )
            security_session.add(security_person)
        else:
            print("SecurityPerson already exists.")
            
        user = auth_session.query(User).filter(User.person == person.id).first()
        if not user:
            print("Creating user...")
            user = User(
                person = person.id,
                username = "admin",
                password = get_password_hash("admin")
            )
            auth_session.add(user)
        else:
            print("User already exists.")

        # Commit both sessions
        demographic_session.commit()
        security_session.commit()
        auth_session.commit()

        print("First user, security person, and role with all permissions created successfully.")
    except Exception as e:
        demographic_session.rollback()
        security_session.rollback()
        auth_session.rollback()
        print(f"Error initializing first user: {e}")
    finally:
        demographic_session.close()
        security_session.close()
        auth_session.close()

if __name__ == "__main__":
    print("Initializing first user...")
    init_first_user()