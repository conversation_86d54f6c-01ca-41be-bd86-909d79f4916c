from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from security_service.app.models.api import Api
from security_service.dependencies.db import SQLALCHEMY_DATABASE_URL

# Create database connection
engine = create_engine(SQLALCHEMY_DATABASE_URL)
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
db = SessionLocal()


def get_service_apis(app, service_name):
    """Extract all API routes from a FastAPI application and handle dynamic parameters."""
    try:
        # Fetch the OpenAPI specification
        openapi = app.openapi()
        apis = []

        # Loop through each path in the OpenAPI specification
        for path, path_item in openapi["paths"].items():
            # Loop through each method (GET, POST, etc.) in the path
            for method, operation in path_item.items():
                if method.upper() not in ["HEAD", "OPTIONS"]:
                    # Process dynamic path parameters (e.g., {staff_id})
                    # Extract parameter names and types
                    parameters = operation.get("parameters", [])
                    path_parameters = []

                    # Look for path parameters in the "parameters" section
                    for param in parameters:
                        if param.get("in") == "path":
                            param_name = param["name"]
                            param_type = param["schema"]["type"]
                            # Check for specific types (e.g., integer, string) and modify the path
                            if param_type == "integer":
                                # You could use a regex pattern for integer matching (e.g., \d+)
                                path_parameters.append(f"{{{param_name}:int}}")
                            else:
                                path_parameters.append(f"{{{param_name}}}")

                    # Replace path parameters in the API path with specific types
                    for i, param in enumerate(path_parameters):
                        path = path.replace(f"{{{parameters[i]['name']}}}", param)

                    # Append the API path with method and service name
                    print({
                        "service_name": service_name.lower(),
                        "method": method.upper(),
                        "api_path": path
                    })
                    
                    apis.append({
                        "service_name": service_name.lower(),
                        "method": method.upper(),
                        "api_path": path
                    })

        return apis
    except Exception as e:
        print(f"Error getting APIs for service {service_name}: {str(e)}")
        return []



def create_apis(apps):
    """Create APIs for the provided FastAPI applications"""
    try:
        apis = []
        for service_name, app in apps.items():
            apis.extend(get_service_apis(app, service_name))

        print("APIs to be created:")
        for api in apis:
            print(f"Service: {api['service_name']}, Path: {api['api_path']}, Method: {api['method']}")

        if not apis:
            print("No APIs found to create")
            return

        # Get existing APIs
        existing_apis = {
            (api.service_name, api.api_path, api.method): api
            for api in db.query(Api).all()
        }

        # Create new APIs only if they don't exist
        new_apis = 0
        for api_data in apis:
            api_key = (api_data["service_name"], api_data["api_path"], api_data["method"])
            if api_key not in existing_apis:
                api = Api(**api_data)
                db.add(api)
                new_apis += 1
        db.commit()
        print(f"Successfully added {new_apis} new API entries")
    except Exception as e:
        print(f"Error creating APIs: {str(e)}")
        db.rollback()
        raise
    finally:
        db.close()
