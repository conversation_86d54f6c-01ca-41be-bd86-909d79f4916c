import pandas as pd
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from demographic_service.app.models import Staff, Person
from demographic_service.dependencies.db import SQLALCHEMY_DATABASE_URL
from security_service.app.models.person_status import PersonStatus
from security_service.app.models.security_person import Security<PERSON>erson
from demographic_service.dependencies.db import (
    SQLALCHEMY_DATABASE_URL as DEMOGRAPHIC_DB_URL,
)
from security_service.dependencies.db import SQLALCHEMY_DATABASE_URL as SECURITY_DB_URL
from sqlalchemy import and_
import shutil
import os

base_dir = os.path.dirname(os.path.abspath(__file__))  # Directory of the current script
staff_file_path = os.path.join(base_dir, "files", "Operations staff list 2025.xlsx")

staff_data = pd.read_excel(staff_file_path, engine="openpyxl").to_dict(orient="records")

engine = create_engine(SQLALCHEMY_DATABASE_URL)
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

# Create database engines
demographic_engine = create_engine(DEMOGRAPHIC_DB_URL)
security_engine = create_engine(SECURITY_DB_URL)

# Create session makers
DemographicSession = sessionmaker(
    autocommit=False, autoflush=False, bind=demographic_engine
)
SecuritySession = sessionmaker(autocommit=False, autoflush=False, bind=security_engine)

base_dir = os.path.dirname(os.path.abspath(__file__))
staff_file_path = os.path.join(base_dir, "files", "Operations staff list 2025.xlsx")
source_images_dir = os.path.join(
    base_dir, "files", "media", "static", "uploads", "images"
)
profile_picture_dir = os.environ.get("DEMOGRAPHIC_DATA_UPLOAD_PATH")


def split_name(full_name):
    parts = full_name.strip().split()

    if not parts:
        return {"first": "", "middle": "", "last": ""}

    # Case 0: FNU case
    if parts[0].upper() == "FNU" and len(parts) > 1:
        return {"first": parts[1], "middle": "", "last": ""}

    # Case 1: Kyrgyz-style (Firstname Patronymic)
    if len(parts) == 3 and parts[2] in ["uulu", "kyzy"]:
        return {
            "first": parts[0],
            "middle": "",
            "last": f"{parts[1]} {parts[2]}",  # e.g., "Tynchtykbek uulu"
        }

    # Case 2: Kyrgyz-style (Firstname Patronymic), 2 parts with suffix
    if len(parts) == 2 and (parts[1].endswith("uulu") or parts[1].endswith("kyzy")):
        return {"first": parts[0], "middle": parts[1], "last": ""}

    # Case 3: Russian-style (Firstname Middlename Lastname)
    if len(parts) == 3:
        return {"first": parts[0], "middle": parts[1], "last": parts[2]}

    # Case 4: Two-part name (Firstname Lastname or Firstname Patronymic)
    if len(parts) == 2:
        return {"first": parts[0], "middle": "", "last": parts[1]}

    # Case 5: More than 3 parts — assume: First Middle Last + extras
    if len(parts) > 3:
        return {"first": parts[0], "middle": " ".join(parts[1:-1]), "last": parts[-1]}

    # Fallback
    return {"first": parts[0], "middle": "", "last": ""}


def safe_strip(value):
    """Safely strip a value, converting it to a string if necessary."""
    if value is None or pd.isna(value):  # Handle NaN or None
        return None
    return str(value).strip()


def load_staff():
    db = SessionLocal()
    security_session = SecuritySession()
    try:
        for staff_entry in staff_data:
            location = safe_strip(staff_entry.get("Location"))
            full_name = split_name(safe_strip(staff_entry.get("Name")))
            first_name = full_name["first"] or None
            middle_name = full_name["middle"] or None
            last_name = full_name["last"] or None
            department = safe_strip(staff_entry.get("Department "))
            position = safe_strip(staff_entry.get("Position "))
            address = safe_strip(staff_entry.get("Adress"))
            phone_number = safe_strip(staff_entry.get("Phone number"))
            email = safe_strip(staff_entry.get("Email"))

            # Check if the person already exists
            filters = []
            if first_name is not None:
                filters.append(Person.name == first_name)
            else:
                filters.append(Person.name.is_(None))

            if last_name is not None:
                filters.append(Person.surname == last_name)
            else:
                filters.append(Person.surname.is_(None))

            if middle_name is not None:
                filters.append(Person.middle_name == middle_name)
            else:
                filters.append(Person.middle_name.is_(None))

            if email is not None:
                filters.append(Person.email == email)
            else:
                filters.append(Person.email.is_(None))

            person = db.query(Person).filter(and_(*filters)).first()

            if not person:
                print("Created person", first_name, middle_name, last_name)
                # Create a new Person entry
                person = Person(
                    name=first_name,
                    surname=last_name,
                    middle_name=middle_name,
                    date_of_birth=None,
                    gender=None,
                    email=email,
                    phone_number=phone_number,
                    nationality=None,
                    profile_picture=None,
                    campus=location,
                )
                db.add(person)
                db.flush()  # Flush to get the person ID

            img_dir = os.path.join(source_images_dir, f"StaffImage_{person.id}")
            if os.path.isdir(img_dir):
                # pick the first file found in the directory
                files = [
                    f
                    for f in os.listdir(img_dir)
                    if os.path.isfile(os.path.join(img_dir, f))
                ]
                if files:
                    src = os.path.join(img_dir, files[0])
                    ext = os.path.splitext(src)[1]
                    dst_filename = f"StaffImage_{person.id}{ext}"
                    dst = os.path.join(profile_picture_dir, dst_filename)
                    shutil.copyfile(src, dst)
                    # store relative path for profile picture
                    person.profile_picture = dst_filename

            # Check if the staff entry already exists
            staff = db.query(Staff).filter(Staff.person_id == person.id).first()
            if not staff:
                # Create a new Staff entry
                print("Created staff", first_name, middle_name, last_name)
                staff = Staff(
                    person_id=person.id,
                    job_title=position,
                    address=address,
                    hire_date=None,
                    department=department,
                )
                db.add(staff)
                db.flush()  # Flush to get the staff ID

            # Create PersonStatus entry
            person_status = (
                security_session.query(PersonStatus)
                .filter(PersonStatus.person_id == person.id)
                .first()
            )
            if not person_status:
                person_status = PersonStatus(person_id=person.id, on_campus=True)
                security_session.add(person_status)

            # Create SecurityPerson entry if department is "security"
            if department and department.lower() == "security":
                security_person = (
                    security_session.query(SecurityPerson)
                    .filter(SecurityPerson.person_id == person.id)
                    .first()
                )
                if not security_person:
                    print(f"Creating SecurityPerson for security staff member {first_name} {last_name}")
                    security_person = SecurityPerson(
                        person_id=person.id,
                        role="security"  # Changed from "security_staff" to "security" to match defined roles
                    )
                    security_session.add(security_person)

        # Commit the transaction
        db.commit()
        security_session.commit()
        print("Staff data loaded successfully.")
    except Exception as e:
        db.rollback()
        security_session.rollback()
        print(f"Error loading staff data: {e}")
    finally:
        db.close()
        security_session.close()


if __name__ == "__main__":
    load_staff()
