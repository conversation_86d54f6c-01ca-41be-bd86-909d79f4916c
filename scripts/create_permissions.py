from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from security_service.app.models.api import Api
from security_service.app.models.role import Role
from security_service.app.models.permission import Permission
from security_service.dependencies.db import SQLALCHEMY_DATABASE_URL


# Create database connection
engine = create_engine(SQLALCHEMY_DATABASE_URL)
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)


def create_permissions():
    """Create permissions for all combinations of APIs and roles"""
    db = SessionLocal()
    try:
        # Get all roles and APIs
        roles = db.query(Role).all()
        apis = db.query(Api).all()

        # Create new permissions only if they don't exist
        new_permissions = 0
        for api in apis:
            for role in roles:
                # Check if the permission already exists
                exists = db.query(Permission).filter_by(api_id=api.id, role_id=role.id).first()
                if not exists:
                    permission = Permission(api_id=api.id, role_id=role.id)
                    db.add(permission)
                    new_permissions += 1

        db.commit()
        print(f"Successfully added {new_permissions} new permissions")
        print(f"Total APIs: {len(apis)}")
        print(f"Total Roles: {len(roles)}")
        print(f"Total possible combinations: {len(apis) * len(roles)}")

    except Exception as e:
        print(f"Error creating permissions: {str(e)}")
        raise  # Re-raise the exception to propagate it
    finally:
        db.close()


if __name__ == "__main__":
    create_permissions()
