from fastapi import FastAP<PERSON>, HTTPException
from card_reader_service.app.crud import (
    create_access_card,
    get_access_card,
    update_access_card,
    delete_access_card,
    get_person_by_card_code,
    SessionLocal
)
from pydantic import BaseModel
from typing import Optional

app = FastAPI()

# Pydantic model for request validation
class AccessCardCreate(BaseModel):
    person_id: int
    card_code: str

class AccessCardUpdate(BaseModel):
    person_id: Optional[int] = None
    card_code: Optional[str] = None

# Create a new access card
@app.post("/access_cards/")
def create_card(card: AccessCardCreate):
    session = SessionLocal()
    db_card = create_access_card(session, card.person_id, card.card_code)
    return db_card

# Get an access card by ID
@app.get("/access_cards/{card_id}")
def read_card(card_id: int):
    session = SessionLocal()
    db_card = get_access_card(session, card_id)
    if db_card is None:
        raise HTTPException(status_code=404, detail="Access card not found")
    return db_card

# Update an access card
@app.put("/access_cards/{card_id}")
def update_card(card_id: int, card: AccessCardUpdate):
    session = SessionLocal()
    db_card = update_access_card(session, card_id, card.person_id, card.card_code)
    if db_card is None:
        raise HTTPException(status_code=404, detail="Access card not found")
    return db_card

# Delete an access card
@app.delete("/access_cards/{card_id}")
def delete_card(card_id: int):
    session = SessionLocal()
    success = delete_access_card(session, card_id)
    if not success:
        raise HTTPException(status_code=404, detail="Access card not found")
    return {"message": "Access card deleted"}

# Get person by card code
@app.get("/person_by_card/{card_code}")
def get_person(card_code: str):
    session = SessionLocal()
    person_id = get_person_by_card_code(session, card_code)
    if person_id is None:
        raise HTTPException(status_code=404, detail="Card code not found")
    return {"person_id": person_id}