import os
from dotenv import load_dotenv

load_dotenv()

redis_server = os.environ.get("redis_server")
redis_port = os.environ.get("redis_port")

db_user = os.environ.get("CARD_READER_DB_USER")
db_password = os.environ.get("CARD_READER_DB_PASSWORD")
db_host = os.environ.get("CARD_READER_DB_HOST")
db_port = os.environ.get("CARD_READER_DB_PORT")
db_name = os.environ.get("CARD_READER_DB_NAME")

image_sub_folder = os.environ.get("image_sub_folder")
batch_size = os.cpu_count()

skip_interval = 1
