from sqlalchemy import Column, Integer, String, DateTime
from sqlalchemy.ext.declarative import declarative_base
from datetime import datetime
from card_reader_service.dependencies.db import Base


class AccessCard(Base):
    __tablename__ = 'access_cards'

    id = Column(Integer, primary_key=True)
    time_created = Column(DateTime, default=datetime.utcnow)
    person_id = Column(Integer, nullable=False)
    card_code = Column(String, unique=True, nullable=False)