from card_reader_service.app.models import AccessCard

def create_access_card(session, person_id, card_code):
    new_card = AccessCard(person_id=person_id, card_code=card_code)
    session.add(new_card)
    session.commit()
    return new_card

def get_access_card(session, card_id):
    return session.query(AccessCard).filter(AccessCard.id == card_id).first()

def update_access_card(session, card_id, person_id=None, card_code=None):
    card = session.query(AccessCard).filter(AccessCard.id == card_id).first()
    if person_id:
        card.person_id = person_id
    if card_code:
        card.card_code = card_code
    session.commit()
    return card

def delete_access_card(session, card_id):
    card = session.query(AccessCard).filter(AccessCard.id == card_id).first()
    session.delete(card)
    session.commit()

def get_person_by_card_code(session, card_code):
    card = session.query(AccessCard).filter(AccessCard.card_code == card_code).first()
    if card:
        return card.person_id
    return None