# auth_dependencies.py
import jwt
from fastapi import Depends, HTTPException, status
from fastapi.security import <PERSON>Auth2<PERSON><PERSON>wordBearer
from sqlalchemy.orm import Session

from auth_service.dependencies.db import get_db
from auth_service.dependencies.redis import get_redis
from auth_service.app.models.User import User
from auth_service.dependencies.auth import SECRET_KEY, ALGORITHM

oauth2_scheme = OAuth2PasswordBearer(tokenUrl="/auth/login")

def get_current_user(
    token: str = Depends(oauth2_scheme),
    redis_client=Depends(get_redis),
    db: Session = Depends(get_db),
) -> User:
    try:
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
        username: str = payload.get("sub")
        if username is None:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid token payload"
            )
        # Проверяем наличие токена в Redis
        if not redis_client.get(f"token:{token}"):
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Token expired or revoked"
            )
        user = db.query(User).filter(User.username == username).first()
        if not user:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="User not found"
            )
    except jwt.PyJWTError:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid token"
        )
    return user

def delete_token_from_redis(token: str, redis_client):
    """
    Deletes the given token from Redis.
    """
    key = f"token:{token}"
    deleted = redis_client.delete(key)
