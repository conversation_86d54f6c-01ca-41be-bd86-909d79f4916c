from typing import List, Optional
from sqlalchemy import Column, Integer, String, Text, ForeignKey, TIMESTAMP
from sqlalchemy.sql import func
from auth_service.dependencies.db import Base


class Model:
    id = Column(Integer, primary_key=True, index=True, autoincrement=True)
    created_at = Column(TIMESTAMP, nullable=False, default=func.now())


class User(Base, Model):
    __tablename__ = "users"
    username = Column(String, index=True, unique=True, nullable=False)
    password = Column(String, nullable=False)
    person = Column(Integer, unique=True, nullable=False)
