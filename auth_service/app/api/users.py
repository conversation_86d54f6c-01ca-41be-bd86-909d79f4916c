# user.py
from fastapi import APIRouter, Depends, HTTPException, status
from pydantic import BaseModel
from sqlalchemy.orm import Session

from auth_service.dependencies.auth import get_password_hash, verify_password
from auth_service.dependencies.db import get_db
from auth_service.dependencies.redis import get_redis
from auth_service.dependencies.auth import oauth2_scheme
from auth_service.app.models.User import User
from auth_service.dependencies.auth_dependencies import get_current_user, delete_token_from_redis
router = APIRouter(prefix="/user", tags=["User"])

class ChangePasswordRequest(BaseModel):
    old_password: str
    new_password: str

class ChangeUsernameRequest(BaseModel):
    new_username: str

@router.get("/username-exists")
async def username_exists(username: str, db: Session = Depends(get_db)):
    exists = db.query(User).filter(User.username == username).first() is not None
    return {"exists": exists}

@router.post("/change-password")
def change_password(
    request: ChangePasswordRequest,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db),
    redis_client=Depends(get_redis),
    token: str = Depends(oauth2_scheme),
):
    if not verify_password(request.old_password, current_user.password):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Old password is incorrect"
        )
    current_user.password = get_password_hash(request.new_password)
    db.commit()
    delete_token_from_redis(token, redis_client)
    return {"message": "Password changed successfully"}

@router.post("/change-username")
def change_username(
    request: ChangeUsernameRequest,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db),
    redis_client=Depends(get_redis),
    token: str = Depends(oauth2_scheme),
):
    # Проверяем, что новое имя пользователя не занято
    existing_user = db.query(User).filter(User.username == request.new_username).first()
    if existing_user:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Username already taken"
        )
    current_user.username = request.new_username
    db.commit()
    db.refresh(current_user)
    delete_token_from_redis(token, redis_client)
    return {"message": "Username changed successfully"}
