import json
from datetime import <PERSON><PERSON><PERSON>
from fastapi import FastAPI, Request, Depends, HTTPException, Response, status
import httpx # type: ignore
from sqlalchemy.orm import Session

from auth_service.dependencies.auth import (
    create_access_token,
    verify_password,
    get_password_hash,
    ACCESS_TOKEN_EXPIRE_MINUTES,
    SECRET_KEY,
    ALGORITHM,
)
from auth_service.app.schemas.Token import Token  # DTO для запросов и ответов
from auth_service.app.schemas.UserCreate import UserCreate  # DTO для запросов и ответов
from auth_service.app.schemas.UserLogin import UserLogin  # DTO для запросов и ответов
from auth_service.app.models.User import User
from auth_service.dependencies.db import get_db
from auth_service.dependencies.redis import get_redis
from auth_service.dependencies.auth_dependencies import get_current_user, oauth2_scheme
from auth_service.app.api.users import router as user_router
from fastapi.middleware.cors import CORSMiddleware
from shared.service_manager import get_services
from fastapi.responses import JSONResponse


app = FastAPI()

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Allow all origins
    allow_credentials=True,
    allow_methods=["*"],  # Allow all HTTP methods
    allow_headers=["*"],  # Allow all headers
)
# Регистрируем роутер для эндпоинтов смены пароля/имени
app.include_router(user_router)

# Эндпоинты регистрации и логина остаются в main.py

@app.post("/auth/logout")
async def logout(
    token: str = Depends(oauth2_scheme),
    redis_client = Depends(get_redis)
):
    key = f"token:{token}"
    deleted = redis_client.delete(key)

    if deleted == 0:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Token not found or already expired"
        )

    return {"detail": "Successfully logged out"}

@app.post("/auth/register")
async def register_user(user: UserCreate, db: Session = Depends(get_db)):
    existing_user = db.query(User).filter(User.username == user.username).first()
    if existing_user:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Username already registered"
        )
    
    # Check if user already exists for this person
    existing_person_user = db.query(User).filter(User.person == user.person).first()
    if existing_person_user:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="User for this person already exists"
        )
    
    # Verify person exists in demographic service
    async with httpx.AsyncClient(timeout=60.0) as client:
        try:
            response = await client.get(
                url=f"http://{get_services()['demographic']}/persons/{user.person}/"
            )
            if response.status_code == status.HTTP_404_NOT_FOUND:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Person with this id doesn't exist."
                )
        except httpx.RequestError as exc:
            raise HTTPException(
                status_code=502,
                detail=f"Error connecting to target service: {exc}"
            )

    hashed_password = get_password_hash(user.password)
    new_user = User(username=user.username, password=hashed_password, person=user.person)
    db.add(new_user)
    db.commit()
    db.refresh(new_user)
    return new_user

@app.post("/auth/login", response_model=Token)
async def login(
    user_login: UserLogin,
    db: Session = Depends(get_db),
    redis_client = Depends(get_redis),
):
    # Поиск пользователя по имени
    user = db.query(User).filter(User.username == user_login.username).first()
    if not user or not verify_password(user_login.password, user.password):
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid credentials"
        )
    # Создание JWT токена
    access_token_expires = timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
    access_token = create_access_token(data={"sub": user.username}, expires_delta=access_token_expires)
    # Сохраняем токен в Redis с временем истечения
    redis_client.setex(f"token:{access_token}", ACCESS_TOKEN_EXPIRE_MINUTES * 1440, user.username)
    return {"access_token": access_token, "token_type": "bearer"}


@app.delete(
    "/auth/user/{person_id}",
    status_code=status.HTTP_200_OK,
    summary="Удалить пользователя по person_id"
)
def delete_user_by_person(
    person_id: int,
    db: Session = Depends(get_db)
):
    """
    Удаляет запись в таблице users, где User.person == person_id.
    Возвращает 200 OK с сообщением об успешном удалении или 404, если пользователь не найден.
    """
    user = db.query(User).filter(User.person == person_id).first()
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"User with person_id={person_id} not found"
        )
    db.delete(user)
    db.commit()
    return JSONResponse(
        status_code=status.HTTP_200_OK,
        content={"message": "User deleted successfully"}
    )


@app.api_route(
    "/srv/{path:path}",
    methods=["GET", "POST", "PUT", "DELETE", "PATCH", "OPTIONS", "HEAD"],
)
async def proxy_request(
    path: str, request: Request, current_user: User = Depends(get_current_user)
):
    print(f"Auth service: Handling request for path {path}")
    path_parts = path.split("/")
    service = path_parts[0]
    path_remainder = "/".join(path_parts[1:])
    if not path_remainder.endswith("/"):
        path_remainder += "/"
    
    try:
        data = get_services()
        service_url = data[service]
    except Exception:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Services configuration error"
        )
    
    target_url = f"http://{service_url}/srv/{path_remainder}"
    
    # Extract query parameters from the original request
    query_params = request.query_params
    if query_params:
        # Append the query parameters to the target URL
        target_url += "?" + "&".join(f"{key}={value}" for key, value in query_params.items())
    
    print(f"Auth service: Target URL is {target_url}")
    
    # Set up headers
    headers = dict(request.headers)
    headers["person_id"] = str(current_user.person)
    
    # Check if this is a multipart/form-data request
    content_type = headers.get("content-type", "")
    
    if content_type.startswith("multipart/form-data"):
        print("Auth service: Detected multipart/form-data request")
        # Don't try to parse the form here - just forward the raw bytes
        body = await request.body()
        
        # Forward the raw bytes with the same content-type headerrow['fields']['person_id']
        async with httpx.AsyncClient(timeout=60.0) as client:
            try:
                response = await client.request(
                    method=request.method,
                    url=target_url,
                    headers=headers,
                    content=body  # Forward the raw body bytes
                )
                print(f"Auth service: Forwarded multipart request, got status {response.status_code}")
            except httpx.RequestError as exc:
                print(f"Auth service: Error connecting to target service: {exc}")
                raise HTTPException(
                    status_code=502,
                    detail=f"Error connecting to target service: {exc}"
                )
    else:
        # For non-multipart requests, forward the body as usual
        body = await request.body()
        async with httpx.AsyncClient(timeout=60.0) as client:
            try:
                response = await client.request(
                    method=request.method,
                    url=target_url,
                    headers=headers,
                    content=body,
                )
            except httpx.RequestError as exc:
                raise HTTPException(
                    status_code=502,
                    detail=f"Error connecting to target service: {exc}"
                )
    
    # Return the response
    return Response(
        content=response.content,
        status_code=response.status_code,
        headers=dict(response.headers)
    )
