"""empty message

Revision ID: 792f73674274
Revises: 830658793a2d
Create Date: 2025-02-23 17:58:00.343839

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '792f73674274'
down_revision = '830658793a2d'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('users', sa.Column('person', sa.Integer(), nullable=False))
    op.create_unique_constraint(None, 'users', ['person'])
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(None, 'users', type_='unique')
    op.drop_column('users', 'person')
    # ### end Alembic commands ###
