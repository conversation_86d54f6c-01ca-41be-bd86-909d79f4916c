from sqlalchemy.orm import Session
from fastapi import HTTPException
from demographic_service.app.models import Person
from .service_manager import get_services
from demographic_service.app.schemas.person import PersonBase
import httpx

async def get_person_by_id(person_id: int):
    try:
        data = get_services()
    except:
        raise Exception("services.json couldn't be loaded")
    
    demographic_url = f"http://{data['demographic']}/persons/{person_id}/"
    
    async with httpx.AsyncClient(timeout=None) as client:
        response = await client.get(demographic_url)
    
    if response.status_code != 200:
        return None
    
    return response.json()