FROM python:3.10

WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \
    gcc \
    postgresql-client \
    && rm -rf /var/lib/apt/lists/*

# Copy requirements first to leverage Docker cache
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Create necessary directories
RUN mkdir -p /app/auth_service \
    /app/demographic_service \
    /app/security_service \
    /app/shared \
    /app/scripts \
    /app/demographic_service/uploads

# Copy services configuration
COPY services.json /app/services.json

# Copy and setup entrypoint script
COPY entrypoint.sh /app/entrypoint.sh
RUN chmod +x /app/entrypoint.sh

# Set Python path to include the app directory
ENV PYTHONPATH=/app

# Expose ports for all services
EXPOSE 8000 8001 8002 8003 8004

# Set the entrypoint
ENTRYPOINT ["/app/entrypoint.sh"] 