.PHONY: build up down logs clean ps restart

debug:
	docker compose up -d --build redis auth_db

# Build the containers
build:
	docker compose up --build

# Start the services
up:
	docker compose up

# Start the services in detached mode
upd:
	docker compose up -d

# Stop the services
down:
	docker compose down

# View logs
logs:
	docker compose logs -f

# Clean up containers and volumes
clean:
	docker compose down -v
	docker system prune -f

# List running containers
ps:
	docker compose ps

# Restart the services
restart:
	docker compose restart

# Rebuild and restart services
rebuild:
	docker compose down
	docker compose build --no-cache
	docker compose up -d

# Show help
help:
	@echo "Available commands:"
	@echo "  make build    - Build the containers"
	@echo "  make up       - Start the services in foreground"
	@echo "  make upd      - Start the services in background"
	@echo "  make down     - Stop the services"
	@echo "  make logs     - View logs"
	@echo "  make clean    - Clean up containers and volumes"
	@echo "  make ps       - List running containers"
	@echo "  make restart  - Restart the services"
	@echo "  make rebuild  - Rebuild and restart services"
	@echo "  make help     - Show this help message" 