import os
from dotenv import load_dotenv
from pathlib import Path

load_dotenv()

redis_server = os.environ.get("redis_server")
redis_port = os.environ.get("redis_port")

db_user = os.environ.get("DEMOGRAPHIC_DATA_DB_USER")
db_password = os.environ.get("DEMOGRAPHIC_DATA_DB_PASSWORD")
db_host = os.environ.get("DEMOGRAPHIC_DATA_DB_HOST")
db_port = os.environ.get("DEMOGRAPHIC_DATA_DB_PORT")
db_name = os.environ.get("DEMOGRAPHIC_DATA_DB_NAME")

batch_size = os.cpu_count()

skip_interval = 1

UPLOAD_PATH = Path(os.path.join(os.getcwd(), os.environ.get("DEMOGRAPHIC_DATA_UPLOAD_PATH")))
UPLOAD_PATH.mkdir(parents=True, exist_ok=True)
