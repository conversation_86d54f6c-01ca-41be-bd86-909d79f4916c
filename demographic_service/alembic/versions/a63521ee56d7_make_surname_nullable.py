"""make surname nullable

Revision ID: a63521ee56d7
Revises: 0434ffd5b060
Create Date: 2025-04-07 22:20:46.102183

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'a63521ee56d7'
down_revision = '0434ffd5b060'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('persons', 'surname',
               existing_type=sa.VARCHAR(),
               nullable=True)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('persons', 'surname',
               existing_type=sa.VARCHAR(),
               nullable=False)
    # ### end Alembic commands ###
