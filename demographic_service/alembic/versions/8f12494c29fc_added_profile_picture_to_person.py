"""Added profile_picture to Person

Revision ID: 8f12494c29fc
Revises: 7ebdc378ac50
Create Date: 2025-02-24 22:56:10.003407

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '8f12494c29fc'
down_revision = '7ebdc378ac50'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('persons', sa.Column('profile_picture', sa.String(), nullable=True))
    op.create_unique_constraint(None, 'staff', ['person_id'])
    op.create_unique_constraint(None, 'students', ['person_id'])
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(None, 'students', type_='unique')
    op.drop_constraint(None, 'staff', type_='unique')
    op.drop_column('persons', 'profile_picture')
    # ### end Alembic commands ###
