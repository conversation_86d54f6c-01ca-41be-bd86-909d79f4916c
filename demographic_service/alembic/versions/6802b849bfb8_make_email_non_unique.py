"""make email non unique

Revision ID: 6802b849bfb8
Revises: a63521ee56d7
Create Date: 2025-04-07 22:22:57.523669

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '6802b849bfb8'
down_revision = 'a63521ee56d7'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint('persons_email_key', 'persons', type_='unique')
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_unique_constraint('persons_email_key', 'persons', ['email'])
    # ### end Alembic commands ###
