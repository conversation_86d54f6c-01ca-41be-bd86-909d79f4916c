"""Add campus field to Person model and made some fields nullable in Person and Staff

Revision ID: 0434ffd5b060
Revises: 8f12494c29fc
Create Date: 2025-04-07 21:59:03.604356

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '0434ffd5b060'
down_revision = '8f12494c29fc'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('persons', sa.Column('campus', sa.String(), nullable=True))
    op.alter_column('persons', 'date_of_birth',
               existing_type=sa.DATE(),
               nullable=True)
    op.alter_column('persons', 'gender',
               existing_type=sa.VARCHAR(),
               nullable=True)
    op.alter_column('persons', 'email',
               existing_type=sa.VARCHAR(),
               nullable=True)
    op.alter_column('persons', 'phone_number',
               existing_type=sa.VARCHAR(),
               nullable=True)
    op.alter_column('persons', 'nationality',
               existing_type=sa.VARCHAR(),
               nullable=True)
    op.alter_column('staff', 'job_title',
               existing_type=sa.VARCHAR(),
               nullable=True)
    op.alter_column('staff', 'address',
               existing_type=sa.VARCHAR(),
               nullable=True)
    op.alter_column('staff', 'hire_date',
               existing_type=sa.DATE(),
               nullable=True)
    op.alter_column('staff', 'department',
               existing_type=sa.VARCHAR(),
               nullable=True)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('staff', 'department',
               existing_type=sa.VARCHAR(),
               nullable=False)
    op.alter_column('staff', 'hire_date',
               existing_type=sa.DATE(),
               nullable=False)
    op.alter_column('staff', 'address',
               existing_type=sa.VARCHAR(),
               nullable=False)
    op.alter_column('staff', 'job_title',
               existing_type=sa.VARCHAR(),
               nullable=False)
    op.alter_column('persons', 'nationality',
               existing_type=sa.VARCHAR(),
               nullable=False)
    op.alter_column('persons', 'phone_number',
               existing_type=sa.VARCHAR(),
               nullable=False)
    op.alter_column('persons', 'email',
               existing_type=sa.VARCHAR(),
               nullable=False)
    op.alter_column('persons', 'gender',
               existing_type=sa.VARCHAR(),
               nullable=False)
    op.alter_column('persons', 'date_of_birth',
               existing_type=sa.DATE(),
               nullable=False)
    op.drop_column('persons', 'campus')
    # ### end Alembic commands ###
