from typing import List, Optional
from sqlalchemy import Column, Date, Integer, String, Text, ForeignKey, TIMESTAMP
from sqlalchemy.sql import func
from demographic_service.dependencies.db import Base
from sqlalchemy.orm import relationship


class Model:
    id = Column(Integer, primary_key=True, index=True, autoincrement=True)
    created_at = Column(TIMESTAMP, nullable=False, default=func.now())


class Person(Base, Model):
    __tablename__ = "persons"

    name = Column(String, nullable=False)
    surname = Column(String, nullable=True)
    middle_name = Column(String, nullable=True)
    date_of_birth = Column(Date, nullable=True)
    gender = Column(String, nullable=True)
    email = Column(String, unique=False, nullable=True)
    phone_number = Column(String, nullable=True)
    nationality = Column(String, nullable=True)
    campus = Column(String, nullable=True)
    
    profile_picture = Column(String, nullable=True)

    student = relationship("Student", uselist=False, back_populates="person")
    staff = relationship("Staff", uselist=False, back_populates="person")
    
    # sqlalchemy -filefield


class Student(Base, Model):
    __tablename__ = "students"

    person_id = Column(Integer, ForeignKey("persons.id"), unique=True, nullable=False)
    graduation_year = Column(Integer, nullable=False)
    department = Column(String, nullable=False)

    person = relationship("Person", uselist=False, back_populates="student")


class Staff(Base, Model):
    __tablename__ = "staff"

    person_id = Column(Integer, ForeignKey("persons.id"), unique=True, nullable=False)
    job_title = Column(String, nullable=True)
    address = Column(String, nullable=True)
    hire_date = Column(Date, nullable=True)
    department = Column(String, nullable=True)

    person = relationship("Person", uselist=False, back_populates="staff")
