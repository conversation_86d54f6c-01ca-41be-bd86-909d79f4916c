from sqlalchemy.orm import Session
from sqlalchemy.exc import SQLAlchemyError
import os
from demographic_service.app.repositories.student_repository import StudentRepository
from demographic_service.app.repositories.person_repository import PersonRepository
from demographic_service.app.services.file_service import FileService
from demographic_service.app.schemas.student import StudentCreate, StudentUpdate
from demographic_service.app.models import Student, Person
from fastapi import HTTPException, UploadFile


class StudentService:
    @staticmethod
    def get_student(db: Session, student_id: int):
        return StudentRepository.get_student(db, student_id)

    @staticmethod
    def get_all_students(db: Session, skip: int = 0, limit: int = 10):
        return StudentRepository.get_all_students(db, skip, limit)

    @staticmethod
    async def create_student(db: Session, student_data: StudentCreate, profile_picture: UploadFile = None):
        file_path = None
        try:
            # Use existing person if person_id is provided
            if getattr(student_data, "person_id", None):
                new_person = db.query(Person).filter(Person.id == student_data.person_id).first()
                if not new_person:
                    raise HTTPException(status_code=404, detail="Person with given id not found")
            elif getattr(student_data, "person", None):
                # Create new person
                new_person = Person(
                    **student_data.person.dict(exclude={"profile_picture"})
                )
                db.add(new_person)
                db.commit()
                db.refresh(new_person)
            else:
                raise HTTPException(status_code=422, detail="Either person or person_id must be provided.")

            # Handle file upload if exists and new_person is present
            if profile_picture:
                file_path = await FileService.save_file(
                    profile_picture.file,
                    parent=f"uploads/profile_pictures/{new_person.id}",
                    file_name="profile_picture.jpg"
                )
                new_person.profile_picture = file_path
                db.commit()
                db.refresh(new_person)

            # Create student
            new_student = Student(
                graduation_year=student_data.graduation_year,
                department=student_data.department,
                person_id=new_person.id
            )
            db.add(new_student)
            db.commit()
            db.refresh(new_student)

            return new_student
        except Exception as e:
            db.rollback()
            if file_path and os.path.exists(file_path):
                os.remove(file_path)
            raise HTTPException(status_code=500, detail=str(e))

    @staticmethod
    async def update_student(db: Session, student_id: int, student_update: StudentUpdate, profile_picture: UploadFile = None):
        file_path = None
        try:
            student = db.query(Student).filter(Student.id == student_id).first()
            if not student:
                raise HTTPException(status_code=404, detail="Student not found")

            # Update student fields
            student_data = student_update.dict(exclude_unset=True)
            if 'person' in student_data:
                person_update = student_data.pop('person')
                person = db.query(Person).filter(Person.id == student.person_id).first()
                if not person:
                    raise HTTPException(status_code=404, detail="Person not found")
                for field, value in person_update.items():
                    if value is not None:  # Only update if value is provided
                        setattr(person, field, value)
                db.commit()
                db.refresh(person)

            # Update remaining student fields
            for field, value in student_data.items():
                setattr(student, field, value)

            # Update profile picture if provided
            if profile_picture:
                person = db.query(Person).filter(Person.id == student.person_id).first()
                if not person:
                    raise HTTPException(status_code=404, detail="Person not found")
                file_path = await FileService.save_file(
                    profile_picture.file,
                    parent=f"uploads/profile_pictures/{person.id}",
                    file_name="profile_picture.jpg"
                )
                person.profile_picture = file_path
                db.commit()
                db.refresh(person)

            db.commit()
            db.refresh(student)
            return student
        except Exception as e:
            db.rollback()
            if file_path and os.path.exists(file_path):
                os.remove(file_path)
            raise HTTPException(status_code=500, detail=str(e))

    @staticmethod
    def delete_student(db: Session, student_id: int):
        return StudentRepository.delete_student(db, student_id)
