from fastapi import UploadFile
from pathlib import Path
import base64
import os
import demographic_service.config as conf
import uuid

UPLOAD_DIR = conf.UPLOAD_PATH
UPLOAD_DIR.mkdir(parents=True, exist_ok=True)

class FileService:
    @staticmethod
    async def save_file(file_object, parent: str = "uploads", file_name: str = None):
        """
        Save an uploaded file to the specified directory
        
        Args:
            file_object (UploadFile.file or SpooledTemporaryFile): The file object to save
            parent (str): Parent directory path
            file_name (str): Optional filename (if not provided, will use a generated name)
            
        Returns:
            str: The path where the file was saved
        """
        # Create directory if it doesn't exist
        os.makedirs(parent, exist_ok=True)
        
        # Generate filename if not provided
        if not file_name:
            file_name = f"{uuid.uuid4()}.jpg"
        
        # Full path to save the file
        full_path = os.path.join(parent, file_name)
        
        # Read the file content
        if hasattr(file_object, "read"):
            # Handle file-like objects (SpooledTemporaryFile, BytesIO, etc.)
            content = file_object.read()
            # Reset the file pointer if it's a real file object with seek
            if hasattr(file_object, "seek"):
                file_object.seek(0)
        else:
            # Assume it's already bytes
            content = file_object
            
        # Ensure we have bytes
        if not isinstance(content, bytes):
            raise ValueError("File content must be bytes")
            
        # Write the content
        with open(full_path, "wb") as f:
            f.write(content)
            
        return full_path
    
    @staticmethod
    def get_file(file_path: str) -> bytes:
        """
        Retrieve the content of a file from the specified path.
        
        Args:
            file_path (str): The path to the file to retrieve.
            
        Returns:
            bytes: The content of the file.
            
        Raises:
            FileNotFoundError: If the file does not exist.
            ValueError: If the file path is invalid.
        """
        # Check if the file exists
        if not os.path.exists(file_path):
            raise FileNotFoundError(f"File not found: {file_path}")
        
        # Read and return the file content
        with open(file_path, "rb") as f:
            return f.read()