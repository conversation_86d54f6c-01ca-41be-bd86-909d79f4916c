from sqlalchemy.orm import Session
from demographic_service.app.repositories.person_repository import PersonRepository
from demographic_service.app.schemas.person import PersonCreate, PersonUpdate


class PersonService:
    @staticmethod
    def get_person(db: Session, person_id: int):
        return PersonRepository.get_person(db, person_id)

    @staticmethod
    def get_all_people(db: Session, skip: int = 0, limit: int = 10):
        return PersonRepository.get_all_people(db, skip, limit)

    @staticmethod
    def create_person(db: Session, person: PersonCreate):
        return PersonRepository.create_person(db, person)

    @staticmethod
    def update_person(db: Session, person_id: int, person_update: PersonUpdate):
        return PersonRepository.update_person(db, person_id, person_update)

    @staticmethod
    def delete_person(db: Session, person_id: int):
        return PersonRepository.delete_person(db, person_id)
