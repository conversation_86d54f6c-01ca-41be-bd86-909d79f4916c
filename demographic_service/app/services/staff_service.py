from sqlalchemy.orm import Session
from demographic_service.app.repositories.staff_repository import StaffRepository
from demographic_service.app.repositories.person_repository import PersonRepository
from demographic_service.app.schemas.staff import StaffCreate, StaffUpdate
from demographic_service.app.services.file_service import FileService
from fastapi import HTTPException
from demographic_service.app.models import Person, Staff


class StaffService:
    @staticmethod
    def get_staff(db: Session, staff_id: int):
        return StaffRepository.get_staff(db, staff_id)

    @staticmethod
    async def get_all_staff(
            db: Session, 
            skip: int = 0, 
            limit: int = 10, 
            search: str = None, 
            sort_by: str = "name", 
            desc: bool = False,
            department: str = None,
            on_campus: bool = None):
        return await StaffRepository.get_all_staff(
            db, 
            skip=skip, 
            limit=limit, 
            search=search, 
            sort_by=sort_by, 
            desc_=desc, 
            department=department,
            on_campus=on_campus
        )

    @staticmethod
    async def create_staff(db: Session, staff_data: StaffCreate):
        if staff_data.person_id is not None:
            person = db.query(Person).filter(Person.id == staff_data.person_id).first()
            if person is None:
                raise HTTPException(status_code=400, detail="Person with this id doesn't exist")
            staff = db.query(Staff).filter(Staff.person_id == person.id).first()
            if staff:
                raise HTTPException(status_code=400, detail="There is a staff registered for this person.")
        else:
            if staff_data.person is None:
                raise HTTPException(status_code=400, detail="Either person data or id should be present")
        
            if PersonRepository.does_email_exist(db, staff_data.person.email):
                raise HTTPException(status_code=400, detail="Email already exists")
            try:
                file_path = None
                if staff_data.person.profile_picture:
                    file_path = await FileService.save_file(staff_data.person.profile_picture, parent = "profile_picture/" + str(staff_data.person.email), file_name = "profile_picture.jpg")
            except:
                raise HTTPException(status_code=400, detail="Profile Picture can't be saved.")
            
            person = Person(
                name=staff_data.person.name,
                surname=staff_data.person.surname,
                middle_name=staff_data.person.middle_name,
                date_of_birth=staff_data.person.date_of_birth,
                gender=staff_data.person.gender,
                email=staff_data.person.email,
                phone_number=staff_data.person.phone_number,
                nationality=staff_data.person.nationality,
                profile_picture=file_path,
            )
            db.add(person)
            db.commit()
            db.refresh(person)

        new_staff = Staff(
            job_title=staff_data.job_title,
            address=staff_data.address,
            hire_date=staff_data.hire_date,
            department=staff_data.department,
            person_id=person.id,
        )
        db.add(new_staff)
        db.commit()
        db.refresh(new_staff)

        return new_staff


    @staticmethod
    def update_staff(db: Session, staff_id: int, staff_update: StaffUpdate):
        return StaffRepository.update_staff(db, staff_id, staff_update)

    @staticmethod
    def delete_staff(db: Session, staff_id: int):
        return StaffRepository.delete_staff(db, staff_id)
