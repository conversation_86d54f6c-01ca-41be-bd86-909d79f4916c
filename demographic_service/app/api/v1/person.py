from fastapi import APIRouter, Depends, HTTPException
from fastapi.responses import FileResponse
import os
from sqlalchemy.orm import Session, joinedload
from demographic_service.app.schemas.person import PersonUpdate, PersonResponse
from demographic_service.app.services.person_service import PersonService
from demographic_service.dependencies.db import get_db
from demographic_service.app.models import Person
from typing import List
import httpx
from shared.service_manager import get_services

router = APIRouter(prefix="/persons", tags=["Persons"])


@router.get("/", response_model=List[PersonResponse])
def get_people(skip: int = 0, limit: int = None, db: Session = Depends(get_db)):
    return PersonService.get_all_people(db, skip, limit)


@router.get("/{person_id}/profile_picture/", response_class=FileResponse)
def get_profile_picture(person_id: int, db: Session = Depends(get_db)):
    person = db.query(Person).filter(Person.id == person_id).first()

    if not person:
        raise HTTPException(status_code=404, detail="Person not found")

    if not person.profile_picture:
        raise HTTPException(status_code=404, detail="Profile picture not found")

    if not os.path.exists(person.profile_picture):
        raise HTTPException(status_code=404, detail="Profile picture file not found")

    return FileResponse(person.profile_picture, media_type="image/jpeg")


@router.get("/{person_id}/")
async def get_person(person_id: int, db: Session = Depends(get_db)):
    person = (
        db.query(Person)
        .options(joinedload(Person.staff), joinedload(Person.student))
        .filter(Person.id == person_id)
        .first()
    )

    if person is None:
        raise HTTPException(status_code=404, detail="Person not found")

    # Determine the role
    if person.staff:
        entity = "staff"
    elif person.student:
        entity = "student"
    else:
        entity = "unknown"

    return {
        "person": PersonResponse.from_orm(person),
        "entity": entity,
        "data": person.staff if entity == "staff" else person.student
    }

# @router.get("/{person_id}/", response_model=PersonResponse)
# def get_person(person_id: int, db: Session = Depends(get_db)):
#     person = PersonService.get_person(db, person_id)
#     if not person:
#         raise HTTPException(status_code=404, detail="Person not found")
#     return person


# @router.post("/", response_model=PersonResponse, status_code=201)
# def create_person(person: PersonCreate, db: Session = Depends(get_db)):
#     return PersonService.create_person(db, person)


@router.put("/{person_id}/", response_model=PersonResponse)
def update_person(person_id: int, person_update: PersonUpdate, db: Session = Depends(get_db)):
    updated_person = PersonService.update_person(db, person_id, person_update)
    if not updated_person:
        raise HTTPException(status_code=404, detail="Person not found")
    return updated_person


@router.delete("/{person_id}/", response_model=PersonResponse)
def delete_person(person_id: int, db: Session = Depends(get_db)):
    deleted_person = PersonService.delete_person(db, person_id)
    if not deleted_person:
        raise HTTPException(status_code=404, detail="Person not found")
    return deleted_person
