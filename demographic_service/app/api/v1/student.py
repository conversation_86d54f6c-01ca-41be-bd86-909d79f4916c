from fastapi import APIRouter, Depends, HTTPException, Query, Form, File, UploadFile
from sqlalchemy.orm import Session
from typing import List, Optional
from pydantic import ValidationError
from demographic_service.app.schemas.student import StudentCreate, StudentUpdate, StudentResponse, StudentCreatePersonIdAndPerson
from demographic_service.app.repositories.student_repository import StudentRepository
from demographic_service.app.services.student_service import StudentService
from demographic_service.dependencies.db import get_db
from demographic_service.app.models import Student
from demographic_service.app.models import Person
from demographic_service.app.schemas.student import PersonIdResponse
from fastapi.encoders import jsonable_encoder
from fastapi.responses import StreamingResponse
import pandas as pd
import json
import io
from datetime import timedelta

router = APIRouter(prefix="/students", tags=["Students"])

@router.get("/export/", response_description="Export students as Excel file")
async def export_students_to_excel(
    skip: int = Query(0, description="Сколько записей пропустить"),
    limit: int = Query(None, description="Сколько записей вернуть"),
    sort_by: str = Query("id", description="Поле для сортировки (например, graduation_year)"),
    order: str = Query("asc", description="Порядок сортировки: 'asc' или 'desc'"),
    search: Optional[str] = Query(None, description="Строка для поиска по имени или фамилии"),
    department: Optional[str] = Query(None, description="Строка для поиска по департаменту"),
    on_campus: Optional[bool] = Query(None, description="Фильтр по статусу нахождения на кампусе"),
    db: Session = Depends(get_db)
):
    students = await StudentRepository.get_all_students(
        db=db,
        skip=skip,
        limit=limit,
        sort_by=sort_by,
        order=order,
        search=search,
        department=department,
        on_campus=on_campus
    )

    # Convert students to dicts (if they are ORM objects)
    student_dicts = []
    for s in students:
        d = s.dict() if hasattr(s, "dict") else vars(s).copy()
        # Expand person fields if needed
        person = None
        if "person" in d and d["person"]:
            person = d.pop("person")
        elif "person_id" in d and d["person_id"]:
            person = db.query(Person).filter(Person.id == d["person_id"]).first()
            d.pop('person_id')
        if person:
            d["name"] = person.name
            d["middle_name"] = person.middle_name
            d["surname"] = person.surname
            d["email"] = person.email
            d["phone_number"] = person.phone_number
            
        d.pop("_sa_instance_state")
        student_dicts.append(d)

    df = pd.DataFrame(student_dicts)

    # Define the desired column order
    column_order = [
        "id",
        "name",
        "middle_name",
        "surname",
        "department",
        "graduation_year",
        "email",
        "phone_number"
    ]

    # Only keep columns that exist in the DataFrame
    column_order = [col for col in column_order if col in df.columns]
    df = df[column_order + [col for col in df.columns if col not in column_order]]
    if "created_at" in df.columns:
        df["created_at"] = df["created_at"].apply(
            lambda dt: (dt + timedelta(hours=6)).strftime("%B %d, %Y %I:%M %p (GMT +6)") if pd.notnull(dt) else ""
        )

    output = io.BytesIO()
    with pd.ExcelWriter(output, engine="xlsxwriter") as writer:
        df.to_excel(writer, index=False, sheet_name="Students")
    output.seek(0)

    return StreamingResponse(
        output,
        media_type="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
        headers={"Content-Disposition": "attachment; filename=students.xlsx"},
    )

@router.get("/", response_model=List[StudentResponse])
async def get_students(
    skip: int = Query(0, description="Сколько записей пропустить"),
    limit: int = Query(None, description="Сколько записей вернуть"),
    sort_by: str = Query("id", description="Поле для сортировки (например, graduation_year)"),
    order: str = Query("asc", description="Порядок сортировки: 'asc' или 'desc'"),
    search: Optional[str] = Query(None, description="Строка для поиска по имени или фамилии"),
    department: Optional[str] = Query(None, description="Строка для поиска по департаменту"),
    on_campus: Optional[str] = Query(None, description="Фильтр по статусу нахождения на кампусе"),
    db: Session = Depends(get_db)
):
    if on_campus is None:
        on_campus = None
    elif on_campus.lower() == "true":
        on_campus = True
    elif on_campus.lower() == "false":
        on_campus = False
    else:
        on_campus = None
    return await StudentRepository.get_all_students(
        db=db,
        skip=skip,
        limit=limit,
        sort_by=sort_by,
        order=order,
        search=search,
        department=department,
        on_campus=on_campus
    )

@router.get("/{student_id}/", response_model=StudentResponse)
def get_student(student_id: int, db: Session = Depends(get_db)):
    student = StudentRepository.get_student(db, student_id)
    if not student:
        raise HTTPException(status_code=404, detail="Student not found")
    return student

@router.post("/", response_model=StudentResponse, status_code=201)
async def create_student(
    data: Optional[str] = Form(..., description="JSON string of student data"),
    profile_picture: UploadFile = File(None),
    db: Session = Depends(get_db)
):
    try:
        student_dict = json.loads(data)
        # Either 'person' (dict) or 'person_id' (int) must be provided
        if not student_dict.get("person") and not student_dict.get("person_id"):
            raise HTTPException(
                status_code=422,
                detail="Either 'person' or 'person_id' must be provided."
            )
        student_data = StudentCreatePersonIdAndPerson(**student_dict)
    except json.JSONDecodeError:
        raise HTTPException(
            status_code=422,
            detail="Invalid JSON data format"
        )
    except ValidationError as e:
        raise HTTPException(
            status_code=422,
            detail=jsonable_encoder(e.errors(), exclude={"input"}),
        )

    try:
        return await StudentService.create_student(db, student_data, profile_picture)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.put("/{student_id}/", response_model=StudentResponse)
async def update_student(
    student_id: int,
    data: str = Form(..., description="JSON string of student update data"),
    profile_picture: UploadFile = File(None),
    db: Session = Depends(get_db)
):
    try:
        student_update_dict = json.loads(data)
        student_update = StudentUpdate(**student_update_dict)
    except json.JSONDecodeError:
        raise HTTPException(
            status_code=422,
            detail="Invalid JSON data format"
        )
    except ValidationError as e:
        raise HTTPException(
            status_code=422,
            detail=jsonable_encoder(e.errors(), exclude={"input"}),
        )

    try:
        return await StudentService.update_student(db, student_id, student_update, profile_picture)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
    
@router.put("/by-person/{person_id}/", response_model=StudentResponse)
async def update_student_by_person(
    person_id: int,
    data: str = Form(..., description="JSON string of student update data"),
    profile_picture: UploadFile = File(None),
    db: Session = Depends(get_db)
):
    # Find the student by person_id
    student = db.query(Student).filter(Student.person_id == person_id).first()
    if not student:
        raise HTTPException(status_code=404, detail="Student not found for this person_id")
    # Call the existing update_student logic
    try:
        student_update_dict = json.loads(data)
        student_update = StudentUpdate(**student_update_dict)
    except json.JSONDecodeError:
        raise HTTPException(
            status_code=422,
            detail="Invalid JSON data format"
        )
    except ValidationError as e:
        raise HTTPException(
            status_code=422,
            detail=jsonable_encoder(e.errors(), exclude={"input"}),
        )
    try:
        return await StudentService.update_student(db, student.id, student_update, profile_picture)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.delete("/{student_id}/", response_model=PersonIdResponse)
def delete_student(student_id: int, db: Session = Depends(get_db)):
    deleted_student = StudentRepository.delete_student(db, student_id)
    if not deleted_student:
        raise HTTPException(status_code=404, detail="Student not found")
    # Return only the person_id in a plain dict to avoid serialization issues
    return {"person": {"id": deleted_student}}
