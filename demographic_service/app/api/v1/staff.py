from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from demographic_service.app.schemas.staff import (
    StaffCreate,
    StaffUpdate,
    StaffResponse,
)
from demographic_service.app.models import Person
from demographic_service.app.services.staff_service import StaffService
from demographic_service.dependencies.db import get_db
from fastapi.responses import StreamingResponse
import io
import pandas as pd

router = APIRouter(prefix="/staff", tags=["Staff"])

@router.get("/", response_model=List[StaffResponse])
async def get_all_staff(
    skip: int = Query(0, ge=0),
    limit: Optional[int] = Query(None, le=1000),
    search: Optional[str] = Query(None),
    sort_by: str = Query("name"),
    desc: bool = Query(False),
    department: Optional[str] = Query(None),
    on_campus: Optional[str] = Query(None),
    db: Session = Depends(get_db),
):
    if on_campus is None:
        on_campus = None
    elif on_campus.lower() == "true":
        on_campus = True
    elif on_campus.lower() == "false":
        on_campus = False
    else:
        on_campus = None
        
    return await StaffService.get_all_staff(
        db,
        skip=skip,
        limit=limit,
        search=search,
        sort_by=sort_by,
        desc=desc,
        department=department,
        on_campus=on_campus
    )

@router.get("/export/", response_description="Export staff as Excel file")
async def export_staff_to_excel(
    skip: int = Query(0, ge=0),
    limit: Optional[int] = Query(None, le=1000),
    search: Optional[str] = Query(None),
    sort_by: str = Query("name"),
    desc: bool = Query(False),
    department: Optional[str] = Query(None),
    db: Session = Depends(get_db),
):
    staff_list = await StaffService.get_all_staff(
        db, skip=skip, limit=limit, search=search, sort_by=sort_by, desc=desc, department=department
    )

    staff_dicts = []
    for s in staff_list:
        d = s.dict() if hasattr(s, "dict") else vars(s).copy()
        person = None
        if "person" in d and d["person"]:
            person = d.pop("person")
        elif "person_id" in d and d["person_id"]:
            person = db.query(Person).filter(Person.id == d["person_id"]).first()
            d.pop('person_id')
        if person:
            d["name"] = person.name
            d["middle_name"] = person.middle_name
            d["surname"] = person.surname
            d["email"] = person.email
            d["phone_number"] = person.phone_number

        d.pop("_sa_instance_state", None)
        d.pop("person_id", None)
        staff_dicts.append(d)

    df = pd.DataFrame(staff_dicts)

    # Define the desired column order
    column_order = [
        "id",
        "name",
        "middle_name",
        "surname",
        "job_title",
        "department",
        "email",
        "phone_number",
        "hire_date",
        "address"
    ]

    # Only keep columns that exist in the DataFrame
    column_order = [col for col in column_order if col in df.columns]
    df = df[column_order + [col for col in df.columns if col not in column_order]]

    if "created_at" in df.columns:
        from datetime import timedelta
        df["created_at"] = df["created_at"].apply(
            lambda dt: (dt + timedelta(hours=6)).strftime("%B %d, %Y %I:%M %p (GMT +6)") if pd.notnull(dt) else ""
        )

    output = io.BytesIO()
    with pd.ExcelWriter(output, engine="xlsxwriter") as writer:
        df.to_excel(writer, index=False, sheet_name="Staff")
    output.seek(0)

    return StreamingResponse(
        output,
        media_type="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
        headers={"Content-Disposition": "attachment; filename=staff.xlsx"},
    )

@router.get("/{staff_id}/", response_model=StaffResponse)
def get_staff(staff_id: int, db: Session = Depends(get_db)):
    """Получить сотрудника по ID."""
    staff = StaffService.get_staff(db, staff_id)
    if not staff:
        raise HTTPException(status_code=404, detail="Staff member not found")
    return staff

@router.post("/", response_model=StaffResponse, status_code=201)
async def create_staff(staff: StaffCreate, db: Session = Depends(get_db)):
    """Создание нового сотрудника с подробной валидацией входных данных."""
    return await StaffService.create_staff(db, staff)

@router.put("/{staff_id}/", response_model=StaffResponse)
def update_staff(
    staff_id: int,
    staff_update: StaffUpdate,
    db: Session = Depends(get_db)
):
    """Обновление данных сотрудника с поддержкой частичного обновления и валидацией."""
    updated = StaffService.update_staff(db, staff_id, staff_update)
    if not updated:
        raise HTTPException(status_code=404, detail="Staff member not found")
    return updated

@router.delete("/{staff_id}/", response_model=StaffResponse)
def delete_staff(staff_id: int, db: Session = Depends(get_db)):
    """Удаление сотрудника по ID."""
    deleted = StaffService.delete_staff(db, staff_id)
    if not deleted:
        raise HTTPException(status_code=404, detail="Staff member not found")
    return deleted
