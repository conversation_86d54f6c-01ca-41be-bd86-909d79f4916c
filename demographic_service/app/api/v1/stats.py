from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from demographic_service.dependencies.db import get_db
from demographic_service.app.models import Staff, Student
import httpx
from shared.service_manager import get_services

router = APIRouter(prefix="/stats", tags=["Statistics"])

@router.get("/campus-status-breakdown/")
async def campus_status_breakdown(db: Session = Depends(get_db)):
    # 1. Call the security service endpoint
    data = get_services()
    SECURITY_URL = f"http://{data['security']}/stats/campus-status/"
    async with httpx.AsyncClient() as client:
        resp = await client.get(SECURITY_URL)
        if resp.status_code != 200:
            raise HTTPException(status_code=502, detail="Could not fetch campus status from security service")
        stats = resp.json()

    on_campus_ids = set(stats["on_campus_person_ids"])
    off_campus_ids = set(stats["off_campus_person_ids"])

    # 2. Get all staff and student person_ids
    staff_ids = set([s.person_id for s in db.query(Staff.person_id).all()])
    student_ids = set([s.person_id for s in db.query(Student.person_id).all()])

    # 3. Count on/off campus for staff and students
    staff_on = len(staff_ids & on_campus_ids)
    staff_off = len(staff_ids & off_campus_ids)
    student_on = len(student_ids & on_campus_ids)
    student_off = len(student_ids & off_campus_ids)

    return {
        "staff_on_campus": staff_on,
        "staff_off_campus": staff_off,
        "student_on_campus": student_on,
        "student_off_campus": student_off,
        "total_staff": len(staff_ids),
        "total_students": len(student_ids),
    }