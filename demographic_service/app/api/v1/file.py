from fastapi import APIRouter, HTTPException
from fastapi.responses import FileResponse
from demographic_service.app.schemas.file import FileRequest
from demographic_service.config import UPLOAD_PATH
from fastapi.staticfiles import StaticFiles
from pathlib import Path

router = APIRouter()

# @router.get("/profile_picture/")
# async def get_image(file_request: FileRequest):
#     if not Path(file_request.path).exists():
#         raise HTTPException(status_code=404, detail="File not found")

#     return FileResponse(file_request.path)

# router.mount("/files/", StaticFiles(directory=UPLOAD_PATH), name="static")
