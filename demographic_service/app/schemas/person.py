from typing import Optional
from datetime import date, datetime, timedelta
from pydantic import BaseModel, EmailStr, field_validator
from enum import Enum


class GenderEnum(str, Enum):
    m = "Male"
    f = "Female"


# Base schema for common fields
class PersonBase(BaseModel):
    id: int
    name: str
    surname: Optional[str] = None
    middle_name: Optional[str] = None
    date_of_birth: Optional[date] = None
    gender: Optional[GenderEnum] = None
    email: Optional[EmailStr] = None
    phone_number: Optional[str] = None
    nationality: Optional[str] = None
    profile_picture: Optional[str] = None  # URL or base64 encoded string

    model_config = {
        "from_attributes": True
    }
    
    @field_validator("name")
    def name_min_length(cls, v):
        if len(v) < 2:
            raise ValueError("Name must be at least 2 characters long")
        return v

    @field_validator("surname")
    def surname_min_length(cls, v):
        if v and len(v) < 2:
            raise ValueError("Surname must be at least 2 characters long")
        return v

    @field_validator("gender")
    def gender_check(cls, v):
        if v and v.lower() not in {"male", "female", "other"}:
            raise ValueError("Gender must be 'male', 'female', or 'other'")
        return v

    # @field_validator("phone_number")
    # def phone_must_be_numeric(cls, v):
    #     if v and not v.isdigit():
    #         raise ValueError("Phone number must contain only digits")
    #     return v

    # @field_validator("profile_picture")
    # def picture_format_check(cls, v):
    #     if v and not (v.startswith("http://") or v.startswith("https://") or v.startswith("data:image/")):
    #         raise ValueError("Profile picture must be a valid URL or base64 image string")
    #     return v


# Schema for creating a new person
class PersonCreate(PersonBase):
    id: Optional[int] = None
    pass


# Schema for updating a person (optional fields)
class PersonUpdate(PersonBase):
    name: Optional[str] = None
    id: Optional[int] = None


# Schema for returning data (including id and timestamps)
class PersonResponse(PersonBase):
    id: int
    created_at: datetime

    model_config = {
        "from_attributes": True,
        "json_encoders": {
            datetime: lambda dt: (dt + timedelta(hours=6)).strftime("%B %d, %Y %I:%M %p (GMT +6)")
        }
    }