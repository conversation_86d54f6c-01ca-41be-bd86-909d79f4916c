from pydantic import BaseModel, model_validator
from datetime import date
from typing import Optional
from demographic_service.app.schemas.person import PersonCreate, PersonResponse, PersonUpdate

class StaffCreate(BaseModel):
    job_title:   str
    address:     Optional[str] = None
    hire_date:   Optional[date] = None
    department:  Optional[str] = None
    person:      Optional[PersonUpdate] = None
    person_id:   Optional[int] = None
    
    @model_validator(mode='after')
    def check_person_or_person_id(cls, model):
        if cls is StaffCreate:
            if not model.person and not model.person_id:
                raise ValueError("Either 'person' or 'person_id' must be provided.")
        return model


class StaffUpdate(StaffCreate):
    job_title:   Optional[str] = None

class StaffResponse(StaffCreate):
    id:          int
    person:      PersonResponse

    model_config = {"from_attributes": True}