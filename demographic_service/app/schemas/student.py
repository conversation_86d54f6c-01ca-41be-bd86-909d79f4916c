from pydantic import BaseModel
from typing import Optional
from datetime import datetime
from .person import Person<PERSON><PERSON>ponse, PersonCreate, PersonUpdate
from enum import Enum

class StudentDepartmentEnum(str, Enum):
    cs = "Computer Science"
    cm = "Communication and Media"
    econ = "Economics"
    es = "Earth and Environmental Sciences"


class StudentBase(BaseModel):
    graduation_year: int
    department: StudentDepartmentEnum


class StudentCreate(StudentBase):
    person: PersonCreate

class StudentCreatePersonIdAndPerson(StudentBase):
    person: Optional[PersonCreate] = None
    person_id: Optional[int] = None


class StudentUpdate(BaseModel):
    graduation_year: Optional[int] = None
    department: Optional[StudentDepartmentEnum] = None
    person: Optional[PersonUpdate] = None


class StudentResponse(BaseModel):
    id: int
    graduation_year: int
    department: StudentDepartmentEnum
    person: PersonResponse  # Nested response

    model_config = {
        "from_attributes": True
    }


class PersonIdResponse(BaseModel):
    person: dict
