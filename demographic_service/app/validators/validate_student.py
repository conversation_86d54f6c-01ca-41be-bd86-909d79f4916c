from demographic_service.app.validators.base import RequestValidator
from demographic_service.app.validators.validate_person import ValidatePerson

class ValidateStudent(RequestValidator):
    def __init__(self, data):
        super().__init__(data)

    def validate(self):
        # Validate required fields
        person_validator = ValidatePerson(self.data.get("person", {})).validate()
        if not person_validator.is_valid():
            # Merge person validation errors into the current errors
            self.errors.update({"person." + key: value for key, value in person_validator.errors.items()})

        
        self.required("graduation_year").is_type("graduation_year", int).ge("graduation_year", 2024)
        self.required("department").is_in('department', [
            'Computer Science',
            'Communication and Media',
            'Earth and Environmental Sciences',
            'Economics'
        ])
        
        return self