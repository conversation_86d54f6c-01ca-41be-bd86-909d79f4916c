import re


class RequestValidator:
    def __init__(self, data):
        self.data = data
        self.errors = {}

    def required(self, field):
        if not self.data.get(field):
            self._add_error(field, f"The {field} field is required.")
        return self

    def not_required(self, field):
        if self.data.get(field):
            return True
        return False

    def ge(self, field, value):
        field_value = self.data.get(field)
        if isinstance(field_value, (int, float)) and field_value < value:
            self._add_error(
                field, f"The {field} should be greater than or equal to {value}")
        return self

    def is_type(self, field, expected_type):
        value = self.data.get(field)
        if value is not None and not isinstance(value, expected_type):
            self._add_error(
                field, f"The {field} must be of type {expected_type.__name__}.")
        return self

    def min_length(self, field, min_len):
        value = self.data.get(field, "")
        if value and len(value) < min_len:
            self._add_error(
                field, f"The {field} must be at least {min_len} characters.")
        return self

    def email(self, field):
        value = self.data.get(field, "")
        if value and not re.match(r"[^@]+@[^@]+\.[^@]+", value):
            self._add_error(
                field, f"The {field} must be a valid email address.")
        return self

    # def does_email_exists(self, field, db):
    #     value = self.data.get(field, "")
    #     if value and not email_already_exists(value):
    #         self._add_error(field, f"The {field} must be a valid email address.")
    #     return self

    def is_in(self, field, allowed_values):
        value = self.data.get(field)
        if value and value not in allowed_values:
            self._add_error(field, f"The selected {field} is invalid. Allowed Options: {allowed_values}")
        return self

    def custom(self, field, func, message):
        value = self.data.get(field)
        if value and not func(value):
            self._add_error(field, message)
        return self

    def is_valid(self):
        return not self.errors

    def _add_error(self, field, message):
        self.errors.setdefault(field, []).append(message)

    def result(self):
        return {
            "message": "Validation failed: see errors for details.",
            "errors": self.errors
        }
