from demographic_service.app.validators.base import RequestValidator

class <PERSON><PERSON><PERSON><PERSON><PERSON>(RequestValidator):
    def __init__(self, data):
        super().__init__(data)

    def validate(self):
        # Validate required fields for Person
        self.required("name").min_length("name", 2)
        if self.not_required("surname"):
            self.min_length("surname", 2)
        if self.not_required("middle_name"):
            self.min_length("middle_name", 2)
        self.required("email").email("email")
        if self.not_required("phone_number"):
            self.min_length("phone_number", 10)
        if self.not_required("gender"):
            self.is_in("gender", ["M", "F"])
        
        return self