from sqlalchemy.orm import Session
from demographic_service.app.models import Student, Person
from demographic_service.app.schemas.student import StudentCreate, StudentUpdate, StudentResponse
from demographic_service.app.repositories.person_repository import PersonRepository
import httpx
from shared.service_manager import get_services
from sqlalchemy import or_, desc, asc


async def get_campus_status():
    services = get_services()
    async with httpx.AsyncClient(timeout=None) as client:
        response = await client.get(
            f"http://{services['security']}/stats/campus-status",
            follow_redirects=True
        )
        return response.json()


class StudentRepository:
    @staticmethod
    def get_student(db: Session, student_id: int):
        return db.query(Student).filter(Student.id == student_id).first()

    @staticmethod
    async def get_all_students(
        db: Session,
        skip: int = 0,
        limit: int = 10,
        sort_by: str = "id",
        order: str = "asc",
        search: str = None,
        department: str = None,
        on_campus: bool = None
    ):
        query = db.query(Student).join(Student.person)
        
        # Apply search filter
        if search:
            query = query.filter(
                or_(
                    Person.name.ilike(f"%{search}%"),
                    Person.surname.ilike(f"%{search}%")
                )
            )
        
        # Apply department filter
        if department:
            query = query.filter(Student.department.ilike(department))
        
        # Apply campus status filter
        if on_campus is not None:
            campus_stats = await get_campus_status()
            person_ids = (campus_stats['on_campus_person_ids'] 
                        if on_campus 
                        else campus_stats['off_campus_person_ids'])
            query = query.filter(Student.person_id.in_(person_ids))
        
        # Apply sorting
        sort_field_map = {
            "id": Student.id,
            "graduation_year": Student.graduation_year,
            "department": Student.department,
            "name": Person.name,
            "surname": Person.surname
        }
        
        sort_column = sort_field_map.get(sort_by, Student.id)
        query = query.order_by(desc(sort_column) if order.lower() == "desc" else asc(sort_column))
        
        # Apply pagination
        query = query.offset(skip)
        if limit is not None:
            query = query.limit(limit)
            
        return query.all()

    @staticmethod
    def create_student(db: Session, student: StudentCreate):
        db_student = Student(**student.model_dump())
        db.add(db_student)
        db.commit()
        db.refresh(db_student)
        return db_student

    @staticmethod
    def update_student(db: Session, student_id: int, student_update: StudentUpdate):
        db_student = db.query(Student).filter(Student.id == student_id).first()
        if not db_student:
            return None
        for key, value in student_update.dict(exclude_unset=True).items():
            setattr(db_student, key, value)
        db.commit()
        db.refresh(db_student)
        return db_student

    @staticmethod
    def delete_student(db: Session, student_id: int) -> int:
        """
        Удаляет запись Student и связанный Person, возвращает person_id.
        """
        db_student = db.query(Student).filter(Student.id == student_id).first()
        if not db_student:
            return None
        person_id = db_student.person_id
        db.delete(db_student)
        db.commit()
        # Удаляем саму персону
        PersonRepository.delete_person(db, person_id)
        return person_id