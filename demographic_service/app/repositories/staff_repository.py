from sqlalchemy.orm import Session, joinedload
from demographic_service.app.models import Staff, Person
from demographic_service.app.schemas.staff import <PERSON><PERSON><PERSON>, StaffUpdate, StaffResponse
from sqlalchemy import or_, desc, asc
from demographic_service.app.repositories.person_repository import PersonRepository
import httpx
from shared.service_manager import get_services


async def get_campus_status():
    services = get_services()
    async with httpx.AsyncClient(timeout=None) as client:
        response = await client.get(
            f"http://{services['security']}/stats/campus-status",
            follow_redirects=True
        )
        return response.json()


class StaffRepository:
    @staticmethod
    def get_staff(db: Session, staff_id: int):
        return db.query(Staff).filter(Staff.id == staff_id).first()

    @staticmethod
    async def get_all_staff(
        db: Session,
        skip: int = 0,
        limit: int = 10,
        search: str = None,
        sort_by: str = "name",
        desc_: bool = False,
        department: str = None,
        on_campus: bool = None
    ):
        print(f"skip: {skip}, limit: {limit}, search: {search}, sort_by: {sort_by}, desc_: {desc_}, department: {department}")
        query = db.query(Staff).join(Staff.person).options(
            joinedload(Staff.person))  # join + eager loading

        # Apply search if search query is provided
        if search:
            query = query.filter(
                or_(
                    Person.name.ilike(f"%{search}%"),
                    Person.surname.ilike(f"%{search}%"),
                    Person.middle_name.ilike(f"%{search}%"),
                    # Person.phone_number.ilike(f"%{search}%"),
                    Person.email.ilike(f"%{search}%"),
                    # Staff.job_title.ilike(f"%{search}%"),
                    # Staff.address.ilike(f"%{search}%"),
                    # Staff.department.ilike(f"%{search}%"),
                )
            )
            
        if department:
            query = query.filter(Staff.department.ilike(department))

        # Apply campus status filter if provided
        if on_campus is not None:
            campus_stats = await get_campus_status()
            person_ids = (campus_stats['on_campus_person_ids'] 
                        if on_campus 
                        else campus_stats['off_campus_person_ids'])
            query = query.filter(Staff.person_id.in_(person_ids))

        # Safe sorting
        sort_field_map = {
            "name": Person.name,
            "surname": Person.surname,
            "email": Person.email,
            "job_title": Staff.job_title,
            "department": Staff.department,
            "hire_date": Staff.hire_date
        }

        sort_column = sort_field_map.get(sort_by, Person.name)

        query = query.order_by(
            desc(sort_column) if desc_ else asc(sort_column))

        # Pagination
        query = query.offset(skip)
        if limit is not None:
            query = query.limit(limit)

        # Default to JSON
        return query.all()

    @staticmethod
    def create_staff(db: Session, staff: StaffCreate):
        db_staff = Staff(**staff.model_dump())
        db.add(db_staff)
        db.commit()
        db.refresh(db_staff)
        return db_staff

    @staticmethod
    def update_staff(db: Session, staff_id: int, staff_update: StaffUpdate):
        db_staff = db.query(Staff).filter(Staff.id == staff_id).first()
        if not db_staff:
            return None

        update_data = staff_update.model_dump(exclude_unset=True)

        # Handle nested 'person' update separately
        person_data = update_data.pop('person', None)
        if person_data:
            for key, value in person_data.items():
                setattr(db_staff.person, key, value)

        for key, value in update_data.items():
            setattr(db_staff, key, value)

        db.commit()
        db.refresh(db_staff)
        return db_staff

    @staticmethod
    def delete_staff(db: Session, staff_id: int):
        db_staff = db.query(Staff).filter(Staff.id == staff_id).first()
        if not db_staff:
            return None
        staff_data = StaffResponse.model_validate(db_staff)
        db.delete(db_staff)
        db.commit()
        PersonRepository.delete_person(db, staff_data.person.id)
        return staff_data
