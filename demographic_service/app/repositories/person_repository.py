from sqlalchemy.orm import Session
from demographic_service.app.models import Person
from demographic_service.app.schemas.person import PersonCreate, PersonUpdate


class PersonRepository:
    @staticmethod
    def get_person(db: Session, person_id: int):
        return db.query(Person).filter(Person.id == person_id).first()

    @staticmethod
    def get_all_people(db: Session, skip: int = 0, limit: int = 10):
        query = db.query(Person).offset(skip)
        if limit is not None:
            query = query.limit(limit)
        return query.all()

    @staticmethod
    def create_person(db: Session, person: PersonCreate):
        db_person = Person(**person.model_dump())
        db.add(db_person)
        db.commit()
        db.refresh(db_person)
        return db_person

    @staticmethod
    def update_person(db: Session, person_id: int, person_update: PersonUpdate):
        db_person = db.query(Person).filter(Person.id == person_id).first()
        if not db_person:
            return None
        for key, value in person_update.dict(exclude_unset=True).items():
            setattr(db_person, key, value)
        db.commit()
        db.refresh(db_person)
        return db_person

    @staticmethod
    def delete_person(db: Session, person_id: int):
        db_person = db.query(Person).filter(Person.id == person_id).first()
        if not db_person:
            return None
        db.delete(db_person)
        db.commit()
        return db_person
    
    @staticmethod
    def does_email_exist(db: Session, email: str) -> bool:
        return db.query(Person).filter(Person.email == email).first() is not None
