from fastapi import <PERSON><PERSON><PERSON>, Request
from demographic_service.app.api.v1 import student, person, staff, file, stats
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from fastapi.exceptions import RequestValidationError
from fastapi.encoders import jsonable_encoder

from fastapi.staticfiles import StaticFiles
from demographic_service.config import UPLOAD_PATH

app = FastAPI()

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Allow all origins
    allow_credentials=True,
    allow_methods=["*"],  # Allow all HTTP methods
    allow_headers=["*"],  # Allow all headers
)

app.include_router(student.router)
app.include_router(person.router)
app.include_router(staff.router)
app.include_router(stats.router)


@app.exception_handler(RequestValidationError)
async def custom_validation_exception_handler(
    request: Request, exc: RequestValidationError
):
    return JSONResponse(
        status_code=422,
        content=jsonable_encoder(
            {
                "error_type": "validation_error",
                "errors": [
                    {
                        "field": ".".join(str(loc) for loc in err["loc"][1:]),
                        "message": err["msg"],
                    }
                    for err in exc.errors()
                ],
            }
        ),
    )


app.mount("/files", StaticFiles(directory=UPLOAD_PATH), name="static")



# @app.get("/persons/{person_id}")
# async def get_person(person_id: int, entity: str, db: Session = Depends(get_db)):
#     if entity == "staff":
#         person = (
#             db.query(Person)
#             .options(joinedload(Person.staff))
#             .filter(Person.id == person_id)
#             .first()
#         )
#     elif entity == "student" or entity == "":
#         person = (
#             db.query(Person)
#             .options(joinedload(Person.student))
#             .filter(Person.id == person_id)
#             .first()
#         )
#     else:
#         raise HTTPException(status_code=404, detail="Wrong entity parameter")
#     if person is None:
#         raise HTTPException(status_code=404, detail="Person not found")
#     return person


# if __name__ == "__main__":
#     uvicorn.run("main:app", host="0.0.0.0", port=8000)
