# UCA Microservices

A microservices-based system for managing university campus access and security.

## Project Structure

```
.
├── auth_service/           # Authentication and authorization service
├── card_reader_service/    # Card reader management service
├── demographic_service/    # Student and staff demographic data service
├── security_service/       # Security personnel and access control service
├── shared/                # Shared utilities and common code
├── scripts/               # Utility scripts for setup and data loading
└── uploads/               # File uploads directory
```

## Prerequisites

- Python 3.10
- Docker and Docker Compose
- PostgreSQL
- Redis

## Environment Setup

1. Copy the example environment file:
```bash
cp .example.env .env
```

2. Update the environment variables in `.env` if needed.

## Building and Running

### Using Docker

1. Build and start the services:
```bash
docker compose up --build
```

Or start in detached mode:
```bash
docker compose up -d
```

2. View logs:
```bash
docker compose logs -f
```

3. Stop services:
```bash
docker compose down
```

4. Clean up containers and volumes:
```bash
docker compose down -v
```

5. List running containers:
```bash
docker compose ps
```

6. Restart services:
```bash
docker compose restart
```

7. Rebuild and restart services:
```bash
docker compose down
docker compose build --no-cache
docker compose up -d
```

> Note: A Makefile is available with additional convenience commands. See the Makefile for more options.

## Database Management

### Running Migrations

For each service, use the following command:
```bash
docker compose exec uca_services alembic -c {service_name}/alembic.ini upgrade head
```

Example:
```bash
docker compose exec uca_services alembic -c auth_service/alembic.ini upgrade head
```

### Initial Setup Scripts

The following scripts are executed automatically when the auth service starts:
- Create roles and permissions
- Create initial user
- Load staff data

To manually load student data:
```bash
docker compose exec uca_services python scripts/load_students_from_backup.py
```

## Service Endpoints

### Auth Service (Port 8000)
- Authentication and authorization
- User management
- Role-based access control

### Demographic Service (Port 8001)
- Student and staff information
- Profile management
- File uploads

### Security Service (Port 8002)
- Security personnel management
- Access control
- Security logs

### Card Reader Service (Port 8003)
- Card reader management
- Access logs
- Device status

## Development

### Debug Mode

For development with hot-reload:
```bash
docker compose up -d --build redis auth_db
```

## License

This project is proprietary and confidential.

