version: '3.7'

services:
  uca_services:
    build: .
    ports:
      - 8000:8000
      - 8001:8001
      - 8002:8002
    volumes:
      - ./auth_service:/app/auth_service
      - ./demographic_service:/app/demographic_service
      - ./security_service:/app/security_service
      - ./shared:/app/shared
      - ./scripts:/app/scripts
      - ./uploads:/app/uploads
      - ./demographic_service/uploads:/app/demographic_service/uploads
    environment:
      # Auth Service
      - AUTH_DB_USER=postgres
      - AUTH_DB_PASSWORD=ATS3cwn5bw
      - AUTH_DB_HOST=auth_db
      - AUTH_DB_PORT=5432
      - AUTH_DB_NAME=auth_db
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      # Demographic Service
      - DEMOGRAPHIC_DATA_DB_USER=postgres
      - DEMOGRAPHIC_DATA_DB_PASSWORD=ATS3cwn5bw
      - DEMOGRAPHIC_DATA_DB_HOST=auth_db
      - DEMOGRAPHIC_DATA_DB_PORT=5432
      - DEMOGRAPHIC_DATA_DB_NAME=demographic_data_db
      - DEMOGRAPHIC_DATA_UPLOAD_PATH=/app/demographic_service/uploads
      # Security Service
      - SECURITY_SERVICE_DB_USER=postgres
      - SECURITY_SERVICE_DB_PASSWORD=ATS3cwn5bw
      - SECURITY_SERVICE_DB_HOST=auth_db
      - SECURITY_SERVICE_DB_PORT=5432
      - SECURITY_SERVICE_DB_NAME=security_service_db
      # Shared Redis Configuration
      - redis_server=redis
      - redis_port=6379
    env_file:
      - .env
    depends_on:
      - auth_db
      - redis

  auth_db:
    image: postgres
    volumes:
      - postgres_data:/var/lib/postgresql/data/
      - ./init-db.sh:/docker-entrypoint-initdb.d/init-db.sh
    environment:
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=ATS3cwn5bw
      - POSTGRES_MULTIPLE_DATABASES=auth_db,security_service_db,demographic_data_db
    ports:
      - "${DB_PORT}:5432"
    networks:
      - default

  redis:
    image: redis:7.4.2
    container_name: redis_service
    restart: always
    ports:
      - "6377:6379"
    volumes:
      - redis_data:/data
    command: ["redis-server", "--appendonly", "yes"]

volumes:
  postgres_data:
  redis_data:

networks:
  default:
    name: uca_microservices 
    driver: bridge