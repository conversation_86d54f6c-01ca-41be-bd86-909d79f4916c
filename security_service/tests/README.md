# Security Service Tests

This directory contains comprehensive unit and integration tests for the security service, with special focus on authentication through the auth service.

## Test Structure

```
tests/
├── __init__.py                 # Test package initialization
├── conftest.py                 # Test configuration and fixtures
├── test_authentication.py     # Authentication and authorization tests
├── test_proxy_service.py       # Proxy service functionality tests
├── test_controllers.py         # Controller endpoint tests
├── test_auth_integration.py    # Auth service integration tests
├── requirements-test.txt       # Test dependencies
└── README.md                   # This file
```

## Test Categories

### 1. Authentication Tests (`test_authentication.py`)
- **Person ID Authentication**: Tests the current authentication mechanism using `person_id` header
- **Role-based Authorization**: Tests permission checking based on user roles
- **Security Person Validation**: Tests validation of security personnel
- **Permission Checking**: Tests API permission validation

### 2. Proxy Service Tests (`test_proxy_service.py`)
- **Request Forwarding**: Tests successful request proxying to target services
- **Authentication Flow**: Tests authentication before forwarding
- **Authorization Checks**: Tests permission validation before forwarding
- **Service Communication**: Tests communication with downstream services
- **Error Handling**: Tests handling of various error scenarios

### 3. Controller Tests (`test_controllers.py`)
- **Role Management**: Tests CRUD operations for roles
- **Permission Management**: Tests permission assignment and updates
- **Security Person Management**: Tests security personnel management
- **Me Endpoint**: Tests user profile endpoint
- **Record Management**: Tests access record creation and retrieval

### 4. Auth Service Integration Tests (`test_auth_integration.py`)
- **JWT Token Validation**: Tests JWT token creation and validation
- **Auth Service Communication**: Tests integration with auth service
- **Cross-Service Authentication**: Tests authentication flow across services
- **Token Lifecycle**: Tests token creation, validation, and expiration

## Key Features Tested

### Authentication Flow
1. **JWT Token Creation**: Tests creation of valid JWT tokens
2. **Token Validation**: Tests validation of JWT tokens with proper secrets
3. **Person ID Mapping**: Tests mapping between JWT tokens and person IDs
4. **Role Assignment**: Tests role-based access control

### Authorization Flow
1. **Permission Checking**: Tests API permission validation
2. **Role-based Access**: Tests access control based on user roles
3. **Resource Protection**: Tests protection of sensitive endpoints
4. **Cross-Service Authorization**: Tests authorization for proxied requests

### Proxy Functionality
1. **Request Forwarding**: Tests forwarding of authenticated requests
2. **Header Preservation**: Tests preservation of request headers
3. **Query Parameter Handling**: Tests handling of query parameters
4. **Response Forwarding**: Tests forwarding of service responses

## Running Tests

### Prerequisites
```bash
# Install test dependencies
pip install -r tests/requirements-test.txt

# Or install with development dependencies
pip install -e ".[test]"
```

### Run All Tests
```bash
# Using pytest directly
pytest tests/ -v

# Using the test runner script
python run_tests.py --type all
```

### Run Specific Test Categories
```bash
# Authentication tests only
python run_tests.py --type authentication

# Proxy service tests only
python run_tests.py --type proxy

# Controller tests only
python run_tests.py --type controllers

# Auth integration tests only
python run_tests.py --type auth_integration
```

### Run with Coverage
```bash
# Install coverage
pip install pytest-cov

# Run tests with coverage
pytest tests/ --cov=security_service --cov-report=html --cov-report=term
```

## Test Configuration

### Database
- Tests use SQLite in-memory database for isolation
- Each test gets a fresh database session
- Database schema is created and destroyed for each test

### Mocking
- External services (auth, demographic, recognition) are mocked
- HTTP clients are mocked to prevent actual network calls
- Service configurations are mocked for consistent testing

### Fixtures
- `db_session`: Provides isolated database session
- `client`: Provides FastAPI test client
- `sample_*`: Provides sample data for testing
- `mock_*`: Provides mocked external dependencies

## Authentication Test Scenarios

### Valid Authentication
- User with valid person_id and role
- Proper permission assignment
- Successful request forwarding

### Invalid Authentication
- Non-existent person_id
- Invalid role assignment
- Missing permissions

### JWT Integration
- Valid JWT token creation
- Token validation with correct secret
- Expired token handling
- Invalid token handling

## Expected Test Results

When all tests pass, you should see:
- ✅ Authentication mechanisms working correctly
- ✅ Authorization checks functioning properly
- ✅ Proxy service forwarding requests correctly
- ✅ Controllers handling requests appropriately
- ✅ Auth service integration working as expected

## Troubleshooting

### Common Issues

1. **Import Errors**: Ensure you're running tests from the security_service directory
2. **Database Errors**: Check that SQLite is available and writable
3. **Mock Failures**: Verify that external service mocks are properly configured
4. **Async Errors**: Ensure pytest-asyncio is installed and configured

### Debug Mode
```bash
# Run tests with debug output
pytest tests/ -v -s --tb=long

# Run specific test with debug
pytest tests/test_authentication.py::TestAuthentication::test_authenticate_valid_person -v -s
```

## Contributing

When adding new tests:
1. Follow the existing naming conventions
2. Use appropriate fixtures from `conftest.py`
3. Mock external dependencies
4. Test both success and failure scenarios
5. Add docstrings explaining what each test validates
