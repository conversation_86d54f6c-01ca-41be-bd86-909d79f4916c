"""
Test configuration and fixtures for security service tests.
"""

import asyncio
import os
import pytest
import pytest_asyncio
from typing import AsyncGenerator, Generator
from unittest.mock import Mock, patch
from fastapi.testclient import TestClient
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from sqlalchemy.pool import StaticPool

# Add parent directory to path for imports
import sys
from pathlib import Path

sys.path.insert(0, str(Path(__file__).parent.parent.parent))

# Import the application and dependencies
from security_service.main import app
from security_service.dependencies.db import get_db, Base
from security_service.app.models.role import Role
from security_service.app.models.api import Api
from security_service.app.models.permission import Permission
from security_service.app.models.security_person import SecurityPerson
from security_service.app.models.person_status import PersonStatus
from security_service.app.models.record import Record

# Test database URL
SQLALCHEMY_DATABASE_URL = "sqlite:///./test.db"

# Create test engine
engine = create_engine(
    SQLALCHEMY_DATABASE_URL,
    connect_args={"check_same_thread": False},
    poolclass=StaticPool,
)

TestingSessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)


@pytest.fixture(scope="session")
def event_loop():
    """Create an instance of the default event loop for the test session."""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


@pytest.fixture
def db_session():
    """Create a test database session."""
    # Create tables
    Base.metadata.create_all(bind=engine)

    # Create session
    session = TestingSessionLocal()

    try:
        yield session
    finally:
        session.close()
        # Clean up tables
        Base.metadata.drop_all(bind=engine)


@pytest.fixture
def client(db_session):
    """Create a test client with database dependency override."""

    def override_get_db():
        try:
            yield db_session
        finally:
            pass

    app.dependency_overrides[get_db] = override_get_db

    with TestClient(app) as test_client:
        yield test_client

    app.dependency_overrides.clear()


@pytest.fixture
def mock_services():
    """Mock the services configuration."""
    services_config = {
        "auth": "localhost:8001",
        "demographic": "localhost:8002",
        "recognition": "localhost:8003",
    }

    with patch("shared.service_manager.get_services", return_value=services_config):
        yield services_config


@pytest.fixture
def sample_role(db_session):
    """Create a sample role for testing."""
    role = Role(name="admin")
    db_session.add(role)
    db_session.commit()
    db_session.refresh(role)
    return role


@pytest.fixture
def sample_api(db_session):
    """Create a sample API for testing."""
    api = Api(service_name="demographic", api_path="/persons/{person_id}", method="GET")
    db_session.add(api)
    db_session.commit()
    db_session.refresh(api)
    return api


@pytest.fixture
def sample_permission(db_session, sample_role, sample_api):
    """Create a sample permission for testing."""
    permission = Permission(
        api_id=sample_api.id, role_id=sample_role.id, is_active=True
    )
    db_session.add(permission)
    db_session.commit()
    db_session.refresh(permission)
    return permission


@pytest.fixture
def sample_security_person(db_session, sample_role):
    """Create a sample security person for testing."""
    security_person = SecurityPerson(person_id=1, role=sample_role.name)
    db_session.add(security_person)
    db_session.commit()
    db_session.refresh(security_person)
    return security_person


@pytest.fixture
def sample_person_status(db_session):
    """Create a sample person status for testing."""
    person_status = PersonStatus(person_id=1, on_campus=True)
    db_session.add(person_status)
    db_session.commit()
    db_session.refresh(person_status)
    return person_status


@pytest.fixture
def auth_headers():
    """Create authentication headers for testing."""
    return {"person_id": "1", "Authorization": "Bearer test_token"}


@pytest.fixture
def mock_auth_service():
    """Mock auth service responses."""
    mock_user = {"id": 1, "username": "testuser", "person": 1}

    with patch("httpx.AsyncClient") as mock_client:
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.json.return_value = mock_user
        mock_client.return_value.__aenter__.return_value.get.return_value = (
            mock_response
        )
        mock_client.return_value.__aenter__.return_value.post.return_value = (
            mock_response
        )
        yield mock_client


@pytest.fixture
def mock_demographic_service():
    """Mock demographic service responses."""
    mock_person = {
        "id": 1,
        "name": "Test",
        "surname": "User",
        "email": "<EMAIL>",
    }

    with patch("httpx.AsyncClient") as mock_client:
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.json.return_value = mock_person
        mock_response.content = b'{"id": 1, "name": "Test", "surname": "User"}'
        mock_response.headers = {"content-type": "application/json"}

        mock_client.return_value.__aenter__.return_value.get.return_value = (
            mock_response
        )
        mock_client.return_value.__aenter__.return_value.request.return_value = (
            mock_response
        )
        yield mock_client
