"""
Tests for proxy service functionality.
"""
import asyncio
import pytest
from unittest.mock import Mock, patch, AsyncMock
from fastapi import Request, HTTPException

from security_service.app.services.proxy_service import ProxyService


class TestProxyService:
    """Test proxy service functionality."""
    
    @pytest.mark.asyncio
    async def test_forward_request_success(
        self, 
        db_session, 
        sample_security_person, 
        sample_permission,
        mock_services,
        mock_demographic_service
    ):
        """Test successful request forwarding."""
        proxy_service = ProxyService(db_session)
        
        # Mock request
        mock_request = Mock(spec=Request)
        mock_request.method = "GET"
        mock_request.query_params = {}
        mock_request.headers = {"content-type": "application/json"}
        mock_request.body = AsyncMock(return_value=b'')
        
        # Test forwarding
        response = await proxy_service.forward_request(
            "demographic/persons/1", 
            mock_request, 
            sample_security_person.person_id
        )
        
        assert response.status_code == 200
    
    @pytest.mark.asyncio
    async def test_forward_request_authentication_failure(self, db_session):
        """Test request forwarding with authentication failure."""
        proxy_service = ProxyService(db_session)
        
        mock_request = Mock(spec=Request)
        
        with pytest.raises(HTTPException) as exc_info:
            await proxy_service.forward_request("demographic/persons/1", mock_request, 999)
        
        assert exc_info.value.status_code == 404
        assert "Security person not found" in str(exc_info.value.detail)
    
    @pytest.mark.asyncio
    async def test_forward_request_authorization_failure(
        self, 
        db_session, 
        sample_security_person,
        sample_api  # No permission created
    ):
        """Test request forwarding with authorization failure."""
        proxy_service = ProxyService(db_session)
        
        mock_request = Mock(spec=Request)
        
        with pytest.raises(HTTPException) as exc_info:
            await proxy_service.forward_request(
                "demographic/persons/1", 
                mock_request, 
                sample_security_person.person_id
            )
        
        assert exc_info.value.status_code == 403
        assert "Permission denied" in str(exc_info.value.detail)
    
    def test_parse_path(self, db_session):
        """Test path parsing functionality."""
        proxy_service = ProxyService(db_session)
        
        service, normalized_path = proxy_service._parse_path("demographic/persons/123")
        
        assert service == "demographic"
        assert normalized_path == "/persons/123"
    
    def test_parse_path_with_trailing_slash(self, db_session):
        """Test path parsing with trailing slash."""
        proxy_service = ProxyService(db_session)
        
        service, normalized_path = proxy_service._parse_path("demographic/persons/123/")
        
        assert service == "demographic"
        assert normalized_path == "/persons/123"
    
    @pytest.mark.asyncio
    async def test_forward_to_service_success(self, db_session, mock_services, mock_demographic_service):
        """Test successful service forwarding."""
        proxy_service = ProxyService(db_session)
        
        mock_request = Mock(spec=Request)
        mock_request.method = "GET"
        mock_request.query_params = {}
        mock_request.headers = {"content-type": "application/json"}
        mock_request.body = AsyncMock(return_value=b'')
        
        response = await proxy_service._forward_to_service(
            "demographic", 
            "demographic/persons/1", 
            mock_request
        )
        
        assert response.status_code == 200
    
    @pytest.mark.asyncio
    async def test_forward_to_service_with_query_params(
        self, 
        db_session, 
        mock_services, 
        mock_demographic_service
    ):
        """Test service forwarding with query parameters."""
        proxy_service = ProxyService(db_session)
        
        mock_request = Mock(spec=Request)
        mock_request.method = "GET"
        mock_request.query_params = {"limit": "10", "offset": "0"}
        mock_request.headers = {"content-type": "application/json"}
        mock_request.body = AsyncMock(return_value=b'')
        
        response = await proxy_service._forward_to_service(
            "demographic", 
            "demographic/persons", 
            mock_request
        )
        
        assert response.status_code == 200
    
    @pytest.mark.asyncio
    async def test_forward_to_service_configuration_error(self, db_session):
        """Test service forwarding with configuration error."""
        proxy_service = ProxyService(db_session)
        
        mock_request = Mock(spec=Request)
        
        with patch("shared.service_manager.get_services", side_effect=Exception("Config error")):
            with pytest.raises(HTTPException) as exc_info:
                await proxy_service._forward_to_service("nonexistent", "test/path", mock_request)
            
            assert exc_info.value.status_code == 500
            assert "Service configuration error" in str(exc_info.value.detail)
    
    @pytest.mark.asyncio
    async def test_forward_to_service_connection_error(self, db_session, mock_services):
        """Test service forwarding with connection error."""
        proxy_service = ProxyService(db_session)
        
        mock_request = Mock(spec=Request)
        mock_request.method = "GET"
        mock_request.query_params = {}
        mock_request.headers = {"content-type": "application/json"}
        mock_request.body = AsyncMock(return_value=b'')
        
        with patch("httpx.AsyncClient") as mock_client:
            mock_client.return_value.__aenter__.return_value.request.side_effect = Exception("Connection error")
            
            with pytest.raises(HTTPException) as exc_info:
                await proxy_service._forward_to_service("demographic", "demographic/persons/1", mock_request)
            
            assert exc_info.value.status_code == 502
            assert "Error connecting to target service" in str(exc_info.value.detail)


class TestProxyEndpoint:
    """Test proxy endpoint integration."""
    
    def test_proxy_endpoint_success(
        self, 
        client, 
        sample_security_person, 
        sample_permission,
        mock_services,
        mock_demographic_service
    ):
        """Test successful proxy request."""
        headers = {"person_id": str(sample_security_person.person_id)}
        
        response = client.get("/srv/demographic/persons/1", headers=headers)
        
        assert response.status_code == 200
    
    def test_proxy_endpoint_post_request(
        self, 
        client, 
        sample_security_person, 
        sample_permission,
        mock_services,
        mock_demographic_service
    ):
        """Test proxy POST request."""
        headers = {"person_id": str(sample_security_person.person_id)}
        data = {"name": "Test", "surname": "User"}
        
        response = client.post("/srv/demographic/persons/", headers=headers, json=data)
        
        assert response.status_code == 200
    
    def test_proxy_endpoint_with_query_params(
        self, 
        client, 
        sample_security_person, 
        sample_permission,
        mock_services,
        mock_demographic_service
    ):
        """Test proxy request with query parameters."""
        headers = {"person_id": str(sample_security_person.person_id)}
        
        response = client.get("/srv/demographic/persons/?limit=10&offset=0", headers=headers)
        
        assert response.status_code == 200
