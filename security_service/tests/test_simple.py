"""
Simple tests to verify test infrastructure works.
"""
import pytest
import sys
from pathlib import Path

# Add parent directory to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent.parent))


def test_imports():
    """Test that we can import basic modules."""
    # Test basic Python functionality
    assert True
    
    # Test that we can import SQLAlchemy
    from sqlalchemy import create_engine
    assert create_engine is not None
    
    # Test that we can import FastAPI
    from fastapi import FastAPI
    assert FastAPI is not None


def test_database_models():
    """Test that we can import and create database models."""
    from sqlalchemy import create_engine, Column, Integer, String
    from sqlalchemy.ext.declarative import declarative_base
    from sqlalchemy.orm import sessionmaker
    
    # Create test database
    engine = create_engine("sqlite:///:memory:")
    Base = declarative_base()
    
    class TestModel(Base):
        __tablename__ = "test"
        id = Column(Integer, primary_key=True)
        name = Column(String)
    
    # Create tables
    Base.metadata.create_all(engine)
    
    # Create session
    Session = sessionmaker(bind=engine)
    session = Session()
    
    # Test model creation
    test_obj = TestModel(name="test")
    session.add(test_obj)
    session.commit()
    
    # Test model retrieval
    retrieved = session.query(TestModel).first()
    assert retrieved.name == "test"
    
    session.close()


def test_jwt_functionality():
    """Test JWT token creation and validation."""
    import jwt
    from datetime import datetime, timedelta
    
    # Test data
    secret = "test_secret"
    algorithm = "HS256"
    payload = {
        "sub": "testuser",
        "person": 1,
        "exp": datetime.utcnow() + timedelta(minutes=30)
    }
    
    # Create token
    token = jwt.encode(payload, secret, algorithm=algorithm)
    assert token is not None
    
    # Decode token
    decoded = jwt.decode(token, secret, algorithms=[algorithm])
    assert decoded["sub"] == "testuser"
    assert decoded["person"] == 1


def test_mock_functionality():
    """Test that mocking works correctly."""
    from unittest.mock import Mock, patch
    
    # Test basic mocking
    mock_obj = Mock()
    mock_obj.test_method.return_value = "mocked"
    
    assert mock_obj.test_method() == "mocked"
    
    # Test patching
    with patch("builtins.print") as mock_print:
        print("test")
        mock_print.assert_called_once_with("test")


@pytest.mark.asyncio
async def test_async_functionality():
    """Test async functionality."""
    import asyncio
    
    async def async_function():
        await asyncio.sleep(0.01)
        return "async_result"
    
    result = await async_function()
    assert result == "async_result"


def test_http_client_mock():
    """Test HTTP client mocking."""
    from unittest.mock import Mock, patch
    
    # Mock httpx client
    with patch("httpx.AsyncClient") as mock_client:
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.json.return_value = {"test": "data"}
        
        mock_client.return_value.__aenter__.return_value.get.return_value = mock_response
        
        # Verify mock setup
        assert mock_response.status_code == 200
        assert mock_response.json() == {"test": "data"}


class TestBasicSecurity:
    """Test basic security functionality."""
    
    def test_password_hashing(self):
        """Test password hashing functionality."""
        from passlib.context import CryptContext
        
        pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")
        
        password = "test_password"
        hashed = pwd_context.hash(password)
        
        assert hashed != password
        assert pwd_context.verify(password, hashed)
        assert not pwd_context.verify("wrong_password", hashed)
    
    def test_token_expiration(self):
        """Test token expiration logic."""
        import jwt
        from datetime import datetime, timedelta
        
        secret = "test_secret"
        
        # Create expired token
        expired_payload = {
            "sub": "testuser",
            "exp": datetime.utcnow() - timedelta(minutes=30)
        }
        expired_token = jwt.encode(expired_payload, secret, algorithm="HS256")
        
        # Verify token is expired
        with pytest.raises(jwt.ExpiredSignatureError):
            jwt.decode(expired_token, secret, algorithms=["HS256"])


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
