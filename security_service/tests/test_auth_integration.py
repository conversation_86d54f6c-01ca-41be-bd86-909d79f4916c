"""
Integration tests for security service with auth service.
"""
import pytest
import jwt
from datetime import datetime, timedelta
from unittest.mock import patch, Mock, AsyncMock

from auth_service.dependencies.auth import SECRET_KEY, ALGORITHM


class TestAuthServiceIntegration:
    """Test integration with auth service."""
    
    def create_test_token(self, username: str = "testuser", person_id: int = 1) -> str:
        """Create a test JWT token."""
        payload = {
            "sub": username,
            "person": person_id,
            "exp": datetime.utcnow() + timedelta(minutes=30)
        }
        return jwt.encode(payload, SECRET_KEY, algorithm=ALGORITHM)
    
    def test_valid_jwt_token_flow(self, client, sample_security_person, mock_services):
        """Test complete authentication flow with valid JWT token."""
        # Create a valid JWT token
        token = self.create_test_token("testuser", sample_security_person.person_id)
        
        # Mock Redis to return the token as valid
        with patch("auth_service.dependencies.redis.get_redis") as mock_redis:
            mock_redis_client = Mock()
            mock_redis_client.get.return_value = "valid"
            mock_redis.return_value = mock_redis_client
            
            # Mock auth service user lookup
            with patch("auth_service.dependencies.db.get_db") as mock_auth_db:
                mock_user = Mock()
                mock_user.username = "testuser"
                mock_user.person = sample_security_person.person_id
                
                mock_session = Mock()
                mock_session.query.return_value.filter.return_value.first.return_value = mock_user
                mock_auth_db.return_value.__enter__.return_value = mock_session
                
                # Test that the token would be valid in auth service context
                payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
                assert payload["sub"] == "testuser"
                assert payload["person"] == sample_security_person.person_id
    
    def test_expired_jwt_token(self):
        """Test handling of expired JWT token."""
        # Create an expired token
        payload = {
            "sub": "testuser",
            "person": 1,
            "exp": datetime.utcnow() - timedelta(minutes=30)  # Expired
        }
        expired_token = jwt.encode(payload, SECRET_KEY, algorithm=ALGORITHM)
        
        # Verify token is expired
        with pytest.raises(jwt.ExpiredSignatureError):
            jwt.decode(expired_token, SECRET_KEY, algorithms=[ALGORITHM])
    
    def test_invalid_jwt_token(self):
        """Test handling of invalid JWT token."""
        invalid_token = "invalid.token.here"
        
        # Verify token is invalid
        with pytest.raises(jwt.InvalidTokenError):
            jwt.decode(invalid_token, SECRET_KEY, algorithms=[ALGORITHM])
    
    def test_token_with_wrong_secret(self):
        """Test handling of token signed with wrong secret."""
        # Create token with wrong secret
        payload = {
            "sub": "testuser",
            "person": 1,
            "exp": datetime.utcnow() + timedelta(minutes=30)
        }
        wrong_token = jwt.encode(payload, "wrong_secret", algorithm=ALGORITHM)
        
        # Verify token is invalid
        with pytest.raises(jwt.InvalidSignatureError):
            jwt.decode(wrong_token, SECRET_KEY, algorithms=[ALGORITHM])


class TestAuthenticationFlow:
    """Test complete authentication flow."""
    
    @patch("httpx.AsyncClient")
    def test_login_and_access_security_service(self, mock_client, client, sample_security_person):
        """Test complete flow: login -> get token -> access security service."""
        # Mock auth service login response
        mock_login_response = Mock()
        mock_login_response.status_code = 200
        mock_login_response.json.return_value = {
            "access_token": "test_token",
            "token_type": "bearer"
        }
        
        # Mock auth service user verification
        mock_user_response = Mock()
        mock_user_response.status_code = 200
        mock_user_response.json.return_value = {
            "id": 1,
            "username": "testuser",
            "person": sample_security_person.person_id
        }
        
        mock_client.return_value.__aenter__.return_value.post.return_value = mock_login_response
        mock_client.return_value.__aenter__.return_value.get.return_value = mock_user_response
        
        # Simulate login process
        login_data = {"username": "testuser", "password": "testpass"}
        
        # In a real scenario, this would go through auth service
        # Here we're testing the structure
        assert login_data["username"] == "testuser"
        assert "password" in login_data
    
    def test_person_id_header_authentication(self, client, sample_security_person, mock_services):
        """Test authentication using person_id header (current implementation)."""
        headers = {"person_id": str(sample_security_person.person_id)}
        
        # Test /me endpoint which uses person_id header
        with patch("httpx.AsyncClient") as mock_client:
            mock_response = Mock()
            mock_response.status_code = 200
            mock_response.json.return_value = {"id": 1, "name": "Test"}
            mock_client.return_value.__aenter__.return_value.get.return_value = mock_response
            
            response = client.get("/me/", headers=headers)
            assert response.status_code == 200
    
    def test_missing_authentication(self, client):
        """Test endpoints without proper authentication."""
        # Test /me endpoint without person_id header
        response = client.get("/me/")
        assert response.status_code == 422  # Validation error
        
        # Test proxy endpoint without person_id header
        response = client.get("/srv/demographic/persons/1")
        assert response.status_code == 422  # Validation error


class TestSecurityServiceAuthDependencies:
    """Test security service authentication dependencies."""
    
    def test_person_id_validation(self, client):
        """Test person_id header validation."""
        # Test with invalid person_id format
        headers = {"person_id": "invalid"}
        response = client.get("/me/", headers=headers)
        assert response.status_code == 422  # Validation error
        
        # Test with negative person_id
        headers = {"person_id": "-1"}
        response = client.get("/me/", headers=headers)
        # Should be handled by business logic, not validation
        assert response.status_code in [404, 500]
    
    def test_database_dependency(self, client, db_session):
        """Test database dependency injection."""
        # Verify that database session is properly injected
        # This is tested implicitly through other tests that use the database
        assert db_session is not None
    
    def test_service_configuration_dependency(self, client, mock_services):
        """Test service configuration dependency."""
        headers = {"person_id": "1"}
        
        # Test with mocked services
        with patch("httpx.AsyncClient") as mock_client:
            mock_response = Mock()
            mock_response.status_code = 200
            mock_response.json.return_value = {"id": 1}
            mock_client.return_value.__aenter__.return_value.get.return_value = mock_response
            
            response = client.get("/me/", headers=headers)
            assert response.status_code == 200


class TestCrossServiceCommunication:
    """Test communication between services."""
    
    @patch("httpx.AsyncClient")
    def test_demographic_service_communication(self, mock_client, client, mock_services):
        """Test communication with demographic service."""
        headers = {"person_id": "1"}
        
        # Mock demographic service response
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.json.return_value = {
            "id": 1,
            "name": "Test",
            "surname": "User"
        }
        mock_client.return_value.__aenter__.return_value.get.return_value = mock_response
        
        response = client.get("/me/", headers=headers)
        assert response.status_code == 200
    
    @patch("httpx.AsyncClient")
    def test_service_communication_failure(self, mock_client, client, mock_services):
        """Test handling of service communication failures."""
        headers = {"person_id": "1"}
        
        # Mock service failure
        mock_client.return_value.__aenter__.return_value.get.side_effect = Exception("Service unavailable")
        
        response = client.get("/me/", headers=headers)
        assert response.status_code == 500
    
    def test_service_configuration_error(self, client):
        """Test handling of service configuration errors."""
        headers = {"person_id": "1"}
        
        # Mock missing service configuration
        with patch("shared.service_manager.get_services", side_effect=Exception("Config error")):
            response = client.get("/me/", headers=headers)
            assert response.status_code == 500
            assert "Services configuration error" in response.json()["detail"]
