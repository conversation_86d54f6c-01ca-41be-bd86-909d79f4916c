"""
Tests for authentication and authorization in security service.
"""
import pytest
from unittest.mock import patch, <PERSON><PERSON>
from fastapi import HTTPEx<PERSON>

from security_service.app.services.proxy_service import ProxyService
from security_service.app.models.security_person import SecurityPerson
from security_service.app.models.role import Role


class TestAuthentication:
    """Test authentication functionality."""
    
    def test_authenticate_valid_person(self, db_session, sample_security_person, sample_role):
        """Test authentication with valid person_id."""
        proxy_service = ProxyService(db_session)
        
        # Test authentication
        role = asyncio.run(proxy_service._authenticate_and_get_role(sample_security_person.person_id))
        
        assert role.id == sample_role.id
        assert role.name == sample_role.name
    
    def test_authenticate_invalid_person(self, db_session):
        """Test authentication with invalid person_id."""
        proxy_service = ProxyService(db_session)
        
        # Test authentication with non-existent person
        with pytest.raises(HTTPException) as exc_info:
            asyncio.run(proxy_service._authenticate_and_get_role(999))
        
        assert exc_info.value.status_code == 404
        assert "Security person not found" in str(exc_info.value.detail)
    
    def test_authenticate_person_without_role(self, db_session):
        """Test authentication with person who has invalid role."""
        # Create security person with non-existent role
        security_person = SecurityPerson(person_id=2, role="nonexistent")
        db_session.add(security_person)
        db_session.commit()
        
        proxy_service = ProxyService(db_session)
        
        with pytest.raises(HTTPException) as exc_info:
            asyncio.run(proxy_service._authenticate_and_get_role(2))
        
        assert exc_info.value.status_code == 404
        assert "Role not found" in str(exc_info.value.detail)


class TestAuthorization:
    """Test authorization functionality."""
    
    def test_check_permissions_valid(self, db_session, sample_role, sample_api, sample_permission):
        """Test permission check with valid permissions."""
        proxy_service = ProxyService(db_session)
        
        # Should not raise exception
        asyncio.run(proxy_service._check_permissions("demographic", "/persons/1", sample_role))
    
    def test_check_permissions_no_api_match(self, db_session, sample_role):
        """Test permission check with no matching API."""
        proxy_service = ProxyService(db_session)
        
        with pytest.raises(HTTPException) as exc_info:
            asyncio.run(proxy_service._check_permissions("nonexistent", "/test", sample_role))
        
        assert exc_info.value.status_code == 404
        assert "API not found" in str(exc_info.value.detail)
    
    def test_check_permissions_no_permission(self, db_session, sample_role, sample_api):
        """Test permission check without proper permissions."""
        proxy_service = ProxyService(db_session)
        
        with pytest.raises(HTTPException) as exc_info:
            asyncio.run(proxy_service._check_permissions("demographic", "/persons/1", sample_role))
        
        assert exc_info.value.status_code == 403
        assert "Permission denied" in str(exc_info.value.detail)
    
    def test_find_matching_api_exact_match(self, db_session, sample_api):
        """Test finding API with exact path match."""
        proxy_service = ProxyService(db_session)
        
        found_api = asyncio.run(proxy_service._find_matching_api("demographic", "/persons/123"))
        
        assert found_api is not None
        assert found_api.id == sample_api.id
    
    def test_find_matching_api_no_match(self, db_session):
        """Test finding API with no match."""
        proxy_service = ProxyService(db_session)
        
        found_api = asyncio.run(proxy_service._find_matching_api("demographic", "/nonexistent"))
        
        assert found_api is None


class TestEndpointAuthentication:
    """Test authentication on actual endpoints."""
    
    def test_proxy_endpoint_without_person_id(self, client):
        """Test proxy endpoint without person_id header."""
        response = client.get("/srv/demographic/persons/1")
        
        assert response.status_code == 422  # Validation error for missing header
    
    def test_proxy_endpoint_with_invalid_person_id(self, client, mock_services):
        """Test proxy endpoint with invalid person_id."""
        headers = {"person_id": "999"}
        response = client.get("/srv/demographic/persons/1", headers=headers)
        
        assert response.status_code == 404
        assert "Security person not found" in response.json()["detail"]
    
    def test_me_endpoint_without_person_id(self, client):
        """Test /me endpoint without person_id header."""
        response = client.get("/me/")
        
        assert response.status_code == 422  # Validation error for missing header
    
    def test_me_endpoint_with_person_id(self, client, mock_services, mock_demographic_service):
        """Test /me endpoint with valid person_id."""
        headers = {"person_id": "1"}
        response = client.get("/me/", headers=headers)
        
        # Should attempt to call demographic service
        assert response.status_code in [200, 500]  # 500 if service is not available


# Import asyncio for async tests
import asyncio
