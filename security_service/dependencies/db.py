import importlib  
from sqlalchemy import create_engine
import sqlalchemy
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
from security_service.config import (
    db_host, db_name, db_password, db_user, db_port,  # Security DB config
    demographic_db_host, demographic_db_name, demographic_db_password, demographic_db_user, demographic_db_port  # Person DB config
)

# Security Service Database
SQLALCHEMY_DATABASE_URL = (
    f"postgresql://{db_user}:{db_password}@{db_host}:{db_port}/{db_name}"
)
engine = create_engine(SQLALCHEMY_DATABASE_URL)
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

# Person Service Database
DEMOGRAPHIC_DATABASE_URL = (
    f"postgresql://{demographic_db_user}:{demographic_db_password}@{demographic_db_host}:{demographic_db_port}/{demographic_db_name}"
)
demographic_engine = create_engine(DEMOGRAPHIC_DATABASE_URL)
DemographicSessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=demographic_engine)

# Base for models
Base = declarative_base()

# Dependency for Security Service DB
def get_db():
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

# Dependency for Person Service DB
def get_demographic_db():
    demographic_db = DemographicSessionLocal()
    try:
        yield demographic_db
    finally:
        demographic_db.close()