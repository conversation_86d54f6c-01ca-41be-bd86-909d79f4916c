#!/usr/bin/env python3
"""
Test runner script for security service.
"""
import sys
import subprocess
import os
from pathlib import Path


def run_tests(test_type="all", verbose=True):
    """
    Run tests for the security service.
    
    Args:
        test_type: Type of tests to run ("all", "unit", "integration", "auth", "proxy")
        verbose: Whether to run tests in verbose mode
    """
    # Change to security service directory
    os.chdir(Path(__file__).parent)
    
    # Base pytest command
    cmd = ["python", "-m", "pytest"]
    
    if verbose:
        cmd.append("-v")
    
    # Add coverage if available
    try:
        import coverage
        cmd.extend(["--cov=security_service", "--cov-report=html", "--cov-report=term"])
    except ImportError:
        print("Coverage not available. Install with: pip install pytest-cov")
    
    # Select tests based on type
    if test_type == "unit":
        cmd.extend(["-m", "unit"])
    elif test_type == "integration":
        cmd.extend(["-m", "integration"])
    elif test_type == "auth":
        cmd.extend(["-m", "auth"])
    elif test_type == "proxy":
        cmd.extend(["-m", "proxy"])
    elif test_type == "all":
        cmd.append("tests/")
    else:
        cmd.append(f"tests/test_{test_type}.py")
    
    print(f"Running command: {' '.join(cmd)}")
    
    # Run tests
    result = subprocess.run(cmd)
    return result.returncode


def main():
    """Main function to handle command line arguments."""
    import argparse
    
    parser = argparse.ArgumentParser(description="Run security service tests")
    parser.add_argument(
        "--type", 
        choices=["all", "unit", "integration", "auth", "proxy", "authentication", "controllers", "auth_integration"],
        default="all",
        help="Type of tests to run"
    )
    parser.add_argument(
        "--quiet", 
        action="store_true",
        help="Run tests in quiet mode"
    )
    
    args = parser.parse_args()
    
    # Run tests
    exit_code = run_tests(test_type=args.type, verbose=not args.quiet)
    sys.exit(exit_code)


if __name__ == "__main__":
    main()
