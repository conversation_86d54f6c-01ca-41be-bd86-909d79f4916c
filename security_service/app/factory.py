"""
Application factory for creating and configuring the FastAPI app.
"""
import logging
from contextlib import asynccontextmanager
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware

from security_service.app.middleware.logging_middleware import LoggingMiddleware
from security_service.app.routers import register_routers
from security_service.app.startup import initialize_application_data


logger = logging.getLogger(__name__)


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager."""
    # Startup
    logger.info("Starting security service...")
    try:
        await initialize_application_data()
        logger.info("Security service started successfully")
    except Exception as e:
        logger.error(f"Failed to start security service: {e}")
        raise
    
    yield
    
    # Shutdown
    logger.info("Shutting down security service...")


def create_app() -> FastAPI:
    """
    Create and configure the FastAPI application.
    
    Returns:
        Configured FastAPI application instance
    """
    app = FastAPI(
        title="Security Service",
        description="Microservice for handling security and access control",
        version="1.0.0",
        lifespan=lifespan,
    )
    
    # Configure middleware
    _configure_middleware(app)
    
    # Register routers
    register_routers(app)
    
    return app


def _configure_middleware(app: FastAPI) -> None:
    """Configure application middleware."""
    # CORS middleware
    app.add_middleware(
        CORSMiddleware,
        allow_origins=["*"],  # TODO: Restrict in production
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )
    
    # Custom logging middleware
    app.add_middleware(LoggingMiddleware)


def _configure_logging() -> None:
    """Configure application logging."""
    logging.basicConfig(
        level=logging.INFO,
        format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
        handlers=[
            logging.StreamHandler(),
            # Add file handler in production
        ]
    )


# Configure logging when module is imported
_configure_logging()
