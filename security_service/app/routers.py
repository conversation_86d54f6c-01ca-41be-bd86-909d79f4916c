"""
Router registration for the security service.
"""
from fastapi import FastAPI

from security_service.app.controllers import (
    role_router,
    permission_router,
    record_router,
    security_person_router,
    face_recognition_router,
    staff_router,
    student_router,
    stats_router,
)
from security_service.app.controllers.me import router as me_router
from security_service.app.v1.proxy import router as proxy_router


def register_routers(app: FastAPI) -> None:
    """
    Register all application routers.
    
    Args:
        app: FastAPI application instance
    """
    # API v1 routers
    app.include_router(proxy_router)
    app.include_router(me_router)
    
    # Entity management routers
    app.include_router(student_router)
    app.include_router(staff_router)
    app.include_router(role_router)
    app.include_router(permission_router)
    app.include_router(record_router)
    app.include_router(security_person_router)
    
    # Feature routers
    app.include_router(face_recognition_router)
    app.include_router(stats_router)
