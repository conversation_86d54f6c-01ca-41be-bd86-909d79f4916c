from pydantic import BaseModel
from typing import Optional
from datetime import datetime, timedelta
from demographic_service.app.schemas.person import PersonBase

class SecurityPersonOut(BaseModel):
    id: int
    name: Optional[str] = None
    surname: Optional[str] = None
    class Config:
        orm_mode = True

class RecordCreate(BaseModel):
    security_person_id: int
    action: str
    method: str
    person_id: int


class Record(BaseModel):
    id: int
    security_person_id: int
    action: str
    timestamp: datetime
    method: str
    person: Optional[PersonBase]
    # Добавлено новое поле для информации о security_person
    security_person: Optional[SecurityPersonOut] = None

    model_config = {
        "from_attributes": True,
        "json_encoders": {
            datetime: lambda dt: (dt + timedelta(hours=6)).strftime("%B %d, %Y %I:%M %p (GMT +6)")
        }
    }
    
    
