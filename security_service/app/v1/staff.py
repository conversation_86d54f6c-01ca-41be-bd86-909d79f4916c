# from fastapi import APIRouter, Depends, HTTPException, status
# from sqlalchemy.orm import Session
# import httpx

# from security_service.dependencies.db import get_db
# from security_service.app.models.security_person import Security<PERSON>erson
# from security_service.app.models.person_status import PersonStatus
# from security_service.config import (
#     DEMOGRAPHIC_SERVICE_URL,
#     AUTH_SERVICE_URL,
#     FACE_RECOGNITION_SERVICE_URL,
# )

# router = APIRouter(prefix="/staff", tags=["Security Staff"])


# @router.delete("/{staff_id}/", status_code=status.HTTP_200_OK)
# async def delete_staff(staff_id: int, db: Session = Depends(get_db)):
#     """
#     То же самое для Staff:
#     1) DELETE /staff/{id}/ на Demographic
#     2) DELETE /auth/user/{person_id}
#     3) удаление своих записей
#     4) DELETE в FaceRecognition
#     """
#     # 1) Demographic
#     try:
#         async with httpx.AsyncClient() as client:
#             resp = await client.delete(f"{DEMOGRAPHIC_SERVICE_URL}/staff/{staff_id}/")
#     except httpx.RequestError as exc:
#         raise HTTPException(status_code=502, detail=f"Demographic unreachable: {exc}")
#     if resp.status_code not in (200, 204):
#         raise HTTPException(status_code=resp.status_code, detail=f"Demo error: {resp.text}")

#     payload = resp.json() if resp.content else {}
#     person_id = payload.get("person", {}).get("id")
#     if not person_id:
#         raise HTTPException(status_code=500, detail="Missing person_id in demo response")

#     # 2) Auth
#     try:
#         async with httpx.AsyncClient() as client:
#             auth_resp = await client.delete(f"{AUTH_SERVICE_URL}/auth/user/{person_id}")
#     except httpx.RequestError as exc:
#         raise HTTPException(status_code=502, detail=f"Auth unreachable: {exc}")
#     if auth_resp.status_code not in (200, 404):
#         raise HTTPException(status_code=auth_resp.status_code, detail=f"Auth error: {auth_resp.text}")

#     # 3) Security DB cleanup
#     ps = db.query(PersonStatus).filter(PersonStatus.person_id == person_id).first()
#     if ps:
#         db.delete(ps)
#     for sp in db.query(SecurityPerson).filter(SecurityPerson.person_id == person_id).all():
#         db.delete(sp)
#     db.commit()

#     # 4) FaceRecognition
#     try:
#         async with httpx.AsyncClient() as client:
#             fr = await client.delete(f"{FACE_RECOGNITION_SERVICE_URL}/person/{person_id}")
#     except httpx.RequestError:
#         fr = None
#     if fr and fr.status_code not in (200, 204, 404):
#         raise HTTPException(status_code=fr.status_code, detail=f"FR error: {fr.text}")

#     return {"message": "Staff deleted successfully"}
