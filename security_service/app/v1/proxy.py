"""
Proxy router for handling authenticated request forwarding.
"""
from fastapi import <PERSON><PERSON><PERSON><PERSON>, Depends, Header, Request
from sqlalchemy.orm import Session

from security_service.app.services.proxy_service import ProxyService
from security_service.dependencies.db import get_db


router = APIRouter(prefix="/srv", tags=["Proxy"])


@router.api_route(
    "/{path:path}",
    methods=["GET", "POST", "PUT", "DELETE", "PATCH", "OPTIONS", "HEAD"],
)
async def proxy_request(
    path: str,
    request: Request,
    person_id: int = Header(alias="person_id"),
    db: Session = Depends(get_db),
):
    """
    Proxy authenticated requests to target services.
    
    This endpoint:
    1. Authenticates the user based on person_id header
    2. Checks permissions for the requested path
    3. Forwards the request to the appropriate service
    4. Returns the response from the target service
    
    Args:
        path: The target path (service/endpoint)
        request: The original request
        person_id: ID of the person making the request (from header)
        db: Database session
        
    Returns:
        Response from the target service
        
    Raises:
        HTTPException: If authentication, authorization, or forwarding fails
    """
    proxy_service = ProxyService(db)
    return await proxy_service.forward_request(path, request, person_id)
