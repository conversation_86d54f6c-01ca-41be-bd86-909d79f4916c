from fastapi import <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, HTTPException, Depends
import httpx
from shared.service_manager import get_services
from security_service.app.services.security_person_service import get_security_person_by_person_id
from security_service.dependencies.db import get_db
from sqlalchemy.orm import Session


router = APIRouter()

@router.get("/me/")
async def get_me(person_id: int = Header(alias="person_id"), db: Session = Depends(get_db)):
    try:
        data = get_services()
    except:
        raise HTTPException(status_code=500, detail="Services configuration error")
    
    demographic_url = f"http://{data['demographic']}/persons/{person_id}/"
    
    async with httpx.AsyncClient() as client:
        response = await client.get(demographic_url)
    
    if response.status_code != 200:
        raise HTTPException(
            status_code=response.status_code,
            detail="Не удалось получить данные пользователя от demographic-data сервиса"
        )
        
    try:
        security_person = get_security_person_by_person_id(db, person_id)
        if security_person is None:
            security_person = {}  # Handle the case where no security person is found
    except Exception as e:
        print(f"Error fetching security person: {e}")  # Log the exception for debugging
        security_person = {}
        
    return {
        **response.json(),
        "security_person": security_person
    }
