from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from security_service.dependencies.db import get_db
from security_service.app.services.role_service import (
    create_role,
    get_roles,
    update_role,
    delete_role,
)
from security_service.app.schemas.role import RoleCreate, UpdateRole

router = APIRouter(prefix="/roles")


@router.post("/")
async def create_role_endpoint(data: RoleCreate, db: Session = Depends(get_db)):
    if not data.name:
        raise HTTPException(status_code=400, detail="Name is required")
    role = create_role(db, data.name)
    return {"id": role.id, "name": role.name}


@router.get("/")
async def get_roles_enpoint(db: Session = Depends(get_db)):
    return get_roles(db)


@router.put("/{role_id}/")
async def update_role_endpoint(
    role_id: int, data: UpdateRole, db: Session = Depends(get_db)
):
    role = update_role(db, role_id, data.name)
    if not role:
        raise HTTPException(status_code=404, detail="Role not found")
    return role


@router.delete("/{role_id}/")
async def delete_role_endpoint(role_id: int, db: Session = Depends(get_db)):
    role = delete_role(db, role_id)
    if not role:
        raise HTTPException(status_code=404, detail="Role not found")
    return {"detail": "Role deleted successfully"}
