from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from security_service.dependencies.db import get_db
from security_service.app.services.security_person_service import (
    create_security_person,
    get_security_person,
    update_security_person,
    delete_security_person,
    get_security_person_by_person_id
)
from security_service.app.schemas.security_person import (
    SecurityPersonCreate,
    SecurityPersonUpdate
)
from shared.service_manager import get_services
import httpx
from typing import List, Dict, Any


router = APIRouter()


@router.post("/security_person/")
async def create_security_person_endpoint(security_data: SecurityPersonCreate, db: Session = Depends(get_db)):
    return create_security_person(db, security_data.role, security_data.person_id)


@router.get("/security_person/", response_model=List[Dict[str, Any]])
async def get_all_security_persons(db: Session = Depends(get_db)):
    from security_service.app.models.security_person import Security<PERSON>erson
    security_persons = db.query(SecurityPerson).all()
    
    try:
        services = get_services()
        demographic_url = f"http://{services['demographic']}/persons/"
        
        response = []
        async with httpx.AsyncClient(timeout=30.0) as client:
            for sp in security_persons:
                try:
                    person_response = await client.get(f"{demographic_url}{sp.person_id}/")
                    sp_dict = {
                        "id": sp.id,
                        "person_id": sp.person_id,
                        "role": sp.role
                    }
                    
                    if person_response.status_code == 200:
                        person_data = person_response.json()
                        if "person" in person_data:
                            sp_dict["person_details"] = {
                                "name": person_data["person"].get("name", ""),
                                "surname": person_data["person"].get("surname", ""),
                                "entity": person_data.get("entity", ""),
                                "entity_data": person_data.get("data", {})
                            }
                        else:
                            sp_dict["person_details"] = {
                                "name": "",
                                "surname": "",
                                "entity": "",
                                "entity_data": {}
                            }
                    else:
                        sp_dict["person_details"] = {
                            "name": "",
                            "surname": "",
                            "entity": "",
                            "entity_data": {}
                        }
                    
                    response.append(sp_dict)
                except Exception:
                    sp_dict = {
                        "id": sp.id,
                        "person_id": sp.person_id,
                        "role": sp.role,
                        "person_details": {
                            "name": "",
                            "surname": "",
                            "entity": "",
                            "entity_data": {}
                        }
                    }
                    response.append(sp_dict)
            
            return response
            
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Failed to fetch person details: {str(e)}"
        )


@router.get("/security_person/{security_person_id}/", response_model=Dict[str, Any])
async def get_security_person_endpoint(security_person_id: int, db: Session = Depends(get_db)):
    security_person = get_security_person(db, security_person_id)
    if not security_person:
        raise HTTPException(status_code=404, detail="Security Person not found")
    
    try:
        services = get_services()
        demographic_url = f"http://{services['demographic']}/persons/"
        
        async with httpx.AsyncClient(timeout=30.0) as client:
            try:
                person_response = await client.get(f"{demographic_url}{security_person.person_id}/")
                sp_dict = {
                    "id": security_person.id,
                    "person_id": security_person.person_id,
                    "role": security_person.role
                }
                
                if person_response.status_code == 200:
                    person_data = person_response.json()
                    if "person" in person_data:
                        sp_dict["person_details"] = {
                            "name": person_data["person"].get("name", ""),
                            "surname": person_data["person"].get("surname", ""),
                            "entity": person_data.get("entity", ""),
                            "entity_data": person_data.get("data", {})
                        }
                    else:
                        sp_dict["person_details"] = {
                            "name": "",
                            "surname": "",
                            "entity": "",
                            "entity_data": {}
                        }
                else:
                    sp_dict["person_details"] = {
                        "name": "",
                        "surname": "",
                        "entity": "",
                        "entity_data": {}
                    }
                
                return sp_dict
            except Exception as e:
                raise HTTPException(
                    status_code=500,
                    detail=f"Failed to fetch person details: {str(e)}"
                )
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Failed to fetch person details: {str(e)}"
        )


@router.get("/security_person_by_person_id/{person_id}/", response_model=Dict[str, Any])
async def get_security_person_by_person_id_endpoint(person_id: int, db: Session = Depends(get_db)):
    security_person = get_security_person_by_person_id(db, person_id)
    if not security_person:
        raise HTTPException(status_code=404, detail="Security Person not found")
    
    try:
        services = get_services()
        demographic_url = f"http://{services['demographic']}/persons/"
        
        async with httpx.AsyncClient(timeout=30.0) as client:
            try:
                person_response = await client.get(f"{demographic_url}{person_id}/")
                sp_dict = {
                    "id": security_person.id,
                    "person_id": security_person.person_id,
                    "role": security_person.role
                }
                
                if person_response.status_code == 200:
                    person_data = person_response.json()
                    if "person" in person_data:
                        sp_dict["person_details"] = {
                            "name": person_data["person"].get("name", ""),
                            "surname": person_data["person"].get("surname", ""),
                            "entity": person_data.get("entity", ""),
                            "entity_data": person_data.get("data", {})
                        }
                    else:
                        sp_dict["person_details"] = {
                            "name": "",
                            "surname": "",
                            "entity": "",
                            "entity_data": {}
                        }
                else:
                    sp_dict["person_details"] = {
                        "name": "",
                        "surname": "",
                        "entity": "",
                        "entity_data": {}
                    }
                
                return sp_dict
            except Exception as e:
                raise HTTPException(
                    status_code=500,
                    detail=f"Failed to fetch person details: {str(e)}"
                )
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Failed to fetch person details: {str(e)}"
        )


@router.put("/security_person/{security_person_id}/")
async def update_security_person_endpoint(
    security_person_id: int,
    security_data: SecurityPersonUpdate,
    db: Session = Depends(get_db)
):
    security_person = update_security_person(
        db,
        security_person_id,
        security_data.role,
        security_data.person_id
    )
    if not security_person:
        raise HTTPException(status_code=404, detail="Security Person not found")
    return security_person


@router.delete("/security_person/{security_person_id}/")
async def delete_security_person_endpoint(security_person_id: int, db: Session = Depends(get_db)):
    security_person = get_security_person(db, security_person_id)
    if not security_person:
        raise HTTPException(status_code=404, detail="Security Person not found")
    return delete_security_person(db, security_person_id)