from security_service.app.controllers.role_controller import router as role_router
from security_service.app.controllers.permission_controller import (
    router as permission_router,
)
from security_service.app.controllers.record_controller import router as record_router
from security_service.app.controllers.security_person_controller import (
    router as security_person_router,
)
from security_service.app.controllers.face_recognition_controller import (
    router as face_recognition_router,
)
from security_service.app.controllers.staff import router as staff_router


__all__ = [
    "role_router",
    "permission_router",
    "record_router",
    "security_person_router",
    "face_recognition_router",
    "staff_router",
]
