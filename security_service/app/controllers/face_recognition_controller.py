import json
from fastapi import (
    APIRouter,
    Depends,
    File,
    Form,
    HTTPException,
    Response,
    UploadFile,
    Request,
    Header,
)
from sqlalchemy.orm import Session
from security_service.dependencies.db import get_db
import httpx

from shared.get_person_by_id import get_person_by_id
from shared.service_manager import get_services

router = APIRouter(prefix="/face_recognition")


@router.post("/staff/train/")
async def train_staff():
    services = get_services()
    async with httpx.AsyncClient(timeout=None) as client:
        response = await client.get(
            f"http://{services['recognition']}/api/staff/train/"
        )

    return Response(
        content=response.content,
        status_code=response.status_code,
        headers=dict(response.headers),
    )


@router.post("/student/train/")
async def train_student():
    services = get_services()
    async with httpx.AsyncClient(timeout=None) as client:
        response = await client.get(
            f"http://{services['recognition']}/api/student/train/"
        )

    return Response(
        content=response.content,
        status_code=response.status_code,
        headers=dict(response.headers),
    )


@router.post("/staff/predict/")
async def predict_staff(file: UploadFile = File(...)):

    services = get_services()
    # Read the uploaded file
    file_bytes = await file.read()

    # Prepare multipart/form-data
    files = {
        "file": (file.filename, file_bytes, file.content_type),
    }

    async with httpx.AsyncClient() as client:
        response = await client.post(
            f"http://{services['recognition']}/api/staff/predict/", files=files
        )
        response_data = response.content.decode("utf-8")
    if isinstance(response_data, int) or (
        isinstance(response_data, str) and response_data.isdigit()
    ):
        person = await get_person_by_id(int(response_data))
        return person

    return Response(
        content=response.content,
        status_code=response.status_code,
        headers=dict(response.headers),
    )


@router.post("/student/predict/")
async def predict_student(
    file: UploadFile = File(...),
):
    services = get_services()
    # Read the uploaded file
    file_bytes = await file.read()

    # Prepare multipart/form-data
    files = {
        "file": (file.filename, file_bytes, file.content_type),
    }

    async with httpx.AsyncClient() as client:
        response = await client.post(
            f"http://{services['recognition']}/api/student/predict/", files=files
        )
        response_data = response.content.decode("utf-8")
    if isinstance(response_data, int) or (
        isinstance(response_data, str) and response_data.isdigit()
    ):
        person = await get_person_by_id(int(response_data))
        return person

    return Response(
        content=response.content,
        status_code=response.status_code,
        headers=dict(response.headers),
    )


@router.get("/staff/image/")
async def get_staff_images(request: Request):
    services = get_services()
    async with httpx.AsyncClient() as client:
        response = await client.get(
            f"http://{services['recognition']}/api/staff/image",
            params=request.query_params,
        )
        response_data = response.json()
    for i, image in enumerate(response_data):
        try:
            response_data[i]["person"] = await get_person_by_id(image["person_id"])
            del response_data[i]["person_id"]
        except:
            pass
        try:
            uploaded_by = await get_person_by_id(image["uploaded_by"])
            if not uploaded_by:
                uploaded_by = image["uploaded_by"]
            response_data[i]["uploaded_by"] = uploaded_by
        except:
            pass
    return response_data


@router.get("/student/image/")
async def get_student_images(request: Request):
    services = get_services()
    async with httpx.AsyncClient() as client:
        response = await client.get(
            f"http://{services['recognition']}/api/student/image",
            params=request.query_params,
        )
        response_data = response.json()
    for i, image in enumerate(response_data):
        try:
            response_data[i]["person"] = await get_person_by_id(image["person_id"])
            del response_data[i]["person_id"]
        except:
            pass
        try:
            response_data[i]["uploaded_by"] = await get_person_by_id(
                image["uploaded_by"]
            )
        except:
            pass
    return response_data


@router.post("/staff/image/")
async def upload_staff_image(
    image: UploadFile = File(...),
    person_id: int = Form(...),
    requested_person_id: int = Header(alias="person_id"),
):
    services = get_services()

    file_bytes = await image.read()

    # Construct the `files` and `data` for multipart/form-data
    files = {
        "image": (image.filename, file_bytes, image.content_type),
    }
    data = {
        "person_id": str(person_id),
        "uploaded_by": str(requested_person_id),
    }

    async with httpx.AsyncClient() as client:
        response = await client.post(
            f"http://{services['recognition']}/api/staff/image", files=files, data=data
        )

    return Response(
        content=response.content,
        status_code=response.status_code,
        headers=dict(response.headers),
    )


@router.post("/student/image/")
async def upload_staff_image(
    image: UploadFile = File(...),
    person_id: int = Form(...),
    requested_person_id: int = Header(alias="person_id"),
):
    services = get_services()
    file_bytes = await image.read()

    # Construct the `files` and `data` for multipart/form-data
    files = {
        "image": (image.filename, file_bytes, image.content_type),
    }

    data = {
        "person_id": str(person_id),
        "uploaded_by": str(requested_person_id),
    }

    async with httpx.AsyncClient() as client:
        response = await client.post(
            f"http://{services['recognition']}/api/student/image",
            files=files,
            data=data,
        )

    return Response(
        content=response.content,
        status_code=response.status_code,
        headers=dict(response.headers),
    )


@router.delete("/staff/image/{image_id}/")
async def delete_staff_by_person_id(image_id: int, request: Request):
    services = get_services()
    async with httpx.AsyncClient() as client:
        response = await client.delete(
            f"http://{services['recognition']}/api/staff/image/{image_id}",
            params=request.query_params,
        )

    return Response(
        content=response.content,
        status_code=response.status_code,
        headers=dict(response.headers),
    )


@router.delete("/student/image/{image_id}/")
async def delete_student(image_id: int, request: Request):
    services = get_services()
    async with httpx.AsyncClient() as client:
        response = await client.delete(
            f"http://{services['recognition']}/api/student/image/{image_id}",
            params=request.query_params,
        )

    return Response(
        content=response.content,
        status_code=response.status_code,
        headers=dict(response.headers),
    )
