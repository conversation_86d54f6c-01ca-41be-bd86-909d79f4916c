from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from security_service.dependencies.db import get_db
from security_service.app.services.record_service import (
    create_record,
    get_records,
)
from security_service.app.schemas.record import RecordCreate
from security_service.app.schemas.record import Record as RecordSchema
from shared.get_person_by_id import get_person_by_id
from security_service.app.services.security_person_service import get_security_person
import asyncio
import httpx
from shared.service_manager import get_services

router = APIRouter()

@router.post("/record/", response_model=RecordSchema)
async def create_record_endpoint(
    record_data: RecordCreate, db: Session = Depends(get_db)
):
    try:
        record = await create_record(
            db,
            record_data.security_person_id,
            record_data.action,
            record_data.method,
            record_data.person_id,
        )
        person = await get_person_by_id(record.person_id)
        sp_obj = get_security_person(db, record.security_person_id)
        if sp_obj:
            sp_person = await get_person_by_id(sp_obj.person_id)
            sp_details = {
                "id": sp_obj.id,
                "name": sp_person["person"]["name"],
                "surname": sp_person["person"]["surname"],
            }
        else:
            sp_details = None

        # Ручное построение объекта RecordSchema вместо from_orm(record)
        record_schema = RecordSchema(
            id=record.id,
            method=record.method,
            person_id=record.person_id,
            action=record.action,
            security_person_id=record.security_person_id,  # обязательно передаем для валидации
            timestamp=record.timestamp,
            person=person["person"],
            security_person=sp_details
        )
        return record_schema
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))


@router.get("/records/")
async def get_records_endpoint(
    method: str = None,
    person_id: int = Query(0, ge=0),
    db: Session = Depends(get_db),
    entity: str = None,
    skip: int = Query(0, ge=0),
    limit: int = Query(10, ge=1, le=100),
):
    # Fetch records from the database with pagination
    records = await get_records(
        db, 
        method=method, 
        person_id=person_id, 
        entity=entity,
        skip=skip,
        limit=limit
    )
    if not records:
        return []

    person_ids = [record.person_id for record in records]
    persons = await asyncio.gather(*[get_person_by_id(pid) for pid in set(person_ids)])
    persons_dict = {}
    
    # Build dictionary of valid person data
    for person in persons:
        if person and isinstance(person, dict) and "person" in person and "id" in person["person"]:
            persons_dict[person["person"]["id"]] = person

    # Fetch entity IDs (staff_id or student_id)
    services = get_services()
    
    async def fetch_entity_id(person_id: int, entity_type: str):
        try:
            async with httpx.AsyncClient(timeout=30.0) as client:
                if entity_type == "staff":
                    url = f"http://{services['demographic']}/staff/"
                else:  # student
                    url = f"http://{services['demographic']}/students/"
                
                response = await client.get(url)
                if response.status_code == 200:
                    data = response.json()
                    if data:
                        for entry in data:
                            if entry.get("person", {}).get("id") == person_id:
                                return entry.get("id")
        except Exception:
            return None
        return None

    # Fetch entity IDs for each person
    entity_tasks = []
    for person in persons:
        if person and isinstance(person, dict) and "entity" in person:
            entity_type = person["entity"].lower()
            if entity_type in ["staff", "student"]:
                entity_tasks.append(fetch_entity_id(person["person"]["id"], entity_type))
    
    entity_ids = await asyncio.gather(*entity_tasks)
    for person, entity_id in zip(persons, entity_ids):
        if person and isinstance(person, dict) and "person" in person and "id" in person["person"]:
            persons_dict[person["person"]["id"]]["entity_id"] = entity_id

    # Build the response with Pydantic models
    try:
        response = []
        for record in records:
            person_data = persons_dict.get(record.person_id, {})
            sp_obj = get_security_person(db, record.security_person_id)
            if sp_obj:
                sp_person = await get_person_by_id(sp_obj.person_id)
                sp_details = {
                    "id": sp_obj.id,
                    "name": sp_person["person"]["name"],
                    "surname": sp_person["person"]["surname"],
                }
            else:
                sp_details = None
            record_data = {
                "id": record.id,
                "method": record.method,
                "person_id": record.person_id,
                "action": record.action,
                "security_person_id": record.security_person_id,
                "timestamp": record.timestamp,
                "person": person_data.get("person", {}),
                "entity": person_data.get("entity"),
                "entity_id": person_data.get("entity_id"),
                "security_person": sp_details,
            }
            response.append(record_data)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

    return response
