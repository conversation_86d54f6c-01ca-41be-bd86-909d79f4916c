import httpx
import json
from fastapi import APIRouter, HTTPException, Depends, Request, Response, status
from sqlalchemy.orm import Session
from shared.service_manager import get_services
from security_service.dependencies.db import get_db
from security_service.app.models.person_status import PersonStatus
from security_service.app.models.security_person import SecurityPerson
from fastapi.responses import StreamingResponse


router = APIRouter(prefix="/students", tags=["Security Students"])

# Загрузка адресов из service.json
try:
    services = get_services()
except Exception:
    raise HTTPException(status_code=500, detail="services.json couldn't be loaded")

DEMOGRAPHIC_SERVICE_URL = services["demographic"]
AUTH_SERVICE_URL = services["auth"]
FACE_RECOGNITION_SERVICE_URL = services["recognition"]

@router.get("/export/")
async def get_student_excel(request: Request):
    data = get_services()
    try:
        async with httpx.AsyncClient(timeout=60.0) as client:
            response = await client.get(
                f"http://{data['demographic']}/students/export/", 
                params=request.query_params,
                follow_redirects=True
            )
            response.raise_for_status()
            
            return StreamingResponse(
                response.aiter_bytes(),
                media_type=response.headers.get(
                    "content-type", 
                    "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
                ),
                headers={
                    "Content-Disposition": response.headers.get(
                        "content-disposition", 
                        "attachment; filename=staff.xlsx"
                    )
                }
            )
    except httpx.TimeoutException:
        raise HTTPException(
            status_code=504,
            detail="The request to the demographic service timed out"
        )
    except httpx.HTTPStatusError as e:
        raise HTTPException(
            status_code=e.response.status_code,
            detail=f"Error from demographic service: {e.response.text}"
        )
    except httpx.RequestError as e:
        raise HTTPException(
            status_code=502,
            detail=f"Error connecting to demographic service: {str(e)}"
        )

@router.post("/")
async def create_student(
    request: Request,
    db: Session = Depends(get_db)
):
    print("Security service: Student creation endpoint called")
    
    # Check if the request is multipart/form-data
    content_type = request.headers.get("content-type", "")
    if content_type.startswith("multipart/form-data"):
        print("Security service: Handling multipart form data")
        try:
            # Get form data
            form = await request.form()
            
            # Extract the JSON data part
            json_data = None
            profile_picture = None
            
            for key, value in form.items():
                if key == "data":
                    # This is the JSON data
                    json_data = json.loads(value)
                elif key == "profile_picture" and hasattr(value, "filename"):
                    # This is the profile picture file
                    profile_picture = value
            
            if not json_data:
                raise HTTPException(status_code=400, detail="Missing 'data' field in form. (security_service/create_student)")
            
            student_data = json_data
            
        except json.JSONDecodeError:
            raise HTTPException(status_code=400, detail="Invalid JSON in 'data' field")
        except Exception as e:
            print(f"Security service error processing form: {str(e)}")
            raise HTTPException(status_code=500, detail=f"Error processing form data: {str(e)}")
    else:
        # For JSON requests
        try:
            student_data = await request.json()
        except json.JSONDecodeError:
            raise HTTPException(status_code=400, detail="Invalid JSON")
    
    print(f"Security service: Processing student data for {student_data.get('person', {}).get('name', 'unknown')}")
    
    # Check username uniqueness if provided
    if "user" in student_data:
        user = student_data["user"]
        async with httpx.AsyncClient() as client:
            username_check = await client.get(
                f"http://{AUTH_SERVICE_URL}/user/username-exists",
                params={"username": user["username"]},
            )
        if username_check.status_code == 200 and username_check.json().get("exists"):
            raise HTTPException(status_code=400, detail="Username already exists")
    
    # Create student in Demographic Service
    if content_type.startswith("multipart/form-data") and profile_picture:
        print("Security service: Forwarding multipart request to demographic service")
        # Forward the multipart request to demographic service
        async with httpx.AsyncClient() as client:
            # Read file content
            file_content = await profile_picture.read()
            
            # Create the multipart request
            files = {
                "profile_picture": (
                    profile_picture.filename,
                    file_content,
                    profile_picture.content_type
                )
            }
            data = {"data": json.dumps(student_data)}
            
            demographic_response = await client.post(
                f"http://{DEMOGRAPHIC_SERVICE_URL}/students/",
                files=files,
                data=data
            )
    else:
        # Forward as JSON
        print("Security service: Forwarding JSON request to demographic service")
        async with httpx.AsyncClient() as client:
            demographic_response = await client.post(
                f"http://{DEMOGRAPHIC_SERVICE_URL}/students/", 
                data={"data": json.dumps(student_data)}
            )
    
    if demographic_response.status_code != 201:
        print(f"Security service: Error from demographic service: {demographic_response.status_code}")
        error_details = json.loads(demographic_response.content)
        return Response(
            content=demographic_response.content,
            status_code=demographic_response.status_code,
            headers=dict(demographic_response.headers),
        )
    
    student_info = demographic_response.json()
    print(f"Security service: Student created successfully in demographic service with ID {student_info.get('id')}")
    
    # Add person status
    person_status = PersonStatus(
        person_id=student_info["person"]["id"],
        on_campus=student_data.get("on_campus", False),
    )
    db.add(person_status)
    db.commit()
    
    # Create user in Auth Service if required
    if "user" in student_data:
        user = student_data["user"]
        user_data = {
            "username": user["username"],
            "password": user["password"],
            "person": student_info["person"]["id"],
        }
        async with httpx.AsyncClient() as client:
            auth_resp = await client.post(
                f"http://{AUTH_SERVICE_URL}/auth/register", 
                json=user_data
            )
        if auth_resp.status_code not in (200, 201):
            raise HTTPException(
                status_code=auth_resp.status_code, 
                detail=auth_resp.json()
            )
        return {**student_info, "user": user}
    
    return student_info


@router.put("/{student_id}/")
async def update_student(
    student_id: int,
    request: Request,
    db: Session = Depends(get_db)
):
    print("Security service: Student update endpoint called")

    content_type = request.headers.get("content-type", "")
    if content_type.startswith("multipart/form-data"):
        print("Security service: Handling multipart form data for update")
        try:
            form = await request.form()
            json_data = None
            profile_picture = None

            for key, value in form.items():
                if key == "data":
                    json_data = json.loads(value)
                elif key == "profile_picture" and hasattr(value, "filename"):
                    profile_picture = value

            if not json_data:
                raise HTTPException(status_code=400, detail="Missing 'data' field in form.")

            student_data = json_data

        except json.JSONDecodeError:
            raise HTTPException(status_code=400, detail="Invalid JSON in 'data' field")
        except Exception as e:
            print(f"Security service error processing form: {str(e)}")
            raise HTTPException(status_code=500, detail=f"Error processing form data: {str(e)}")

    else:
        try:
            student_data = await request.json()
        except json.JSONDecodeError:
            raise HTTPException(status_code=400, detail="Invalid JSON")

    print(f"Security service: Updating student with ID {student_id}")

    # Forward to Demographic Service
    if content_type.startswith("multipart/form-data") and profile_picture:
        print("Security service: Forwarding multipart update request to demographic service")
        async with httpx.AsyncClient() as client:
            file_content = await profile_picture.read()
            files = {
                "profile_picture": (
                    profile_picture.filename,
                    file_content,
                    profile_picture.content_type
                )
            }
            data = {"data": json.dumps(student_data)}

            demographic_response = await client.put(
                f"http://{DEMOGRAPHIC_SERVICE_URL}/students/{student_id}/",
                files=files,
                data=data
            )
    else:
        print("Security service: Forwarding JSON update request to demographic service")
        async with httpx.AsyncClient() as client:
            demographic_response = await client.put(
                f"http://{DEMOGRAPHIC_SERVICE_URL}/students/{student_id}/",
                data={"data": json.dumps(student_data)}
            )

    if demographic_response.status_code not in (200, 201):
        print(f"Security service: Error from demographic service: {demographic_response.status_code}")
        return Response(
            content=demographic_response.content,
            status_code=demographic_response.status_code,
            headers=dict(demographic_response.headers),
        )

    student_info = demographic_response.json()
    print(f"Security service: Student updated successfully with ID {student_info.get('id')}")

    # Optional: handle user update in auth service if needed (not implemented here)
    # If you want to update password or username, consider a patch call to auth

    return student_info


@router.get("/")
async def list_students(request: Request, db: Session = Depends(get_db)):
    # Получаем список из Demographic Service
    async with httpx.AsyncClient(timeout=None) as client:
        try:
            resp = await client.get(
                f"http://{DEMOGRAPHIC_SERVICE_URL}/students/", params=request.query_params
            )
            resp.raise_for_status()
            students = resp.json()
            for i, single_staff in enumerate(students):
                person_status = (
                    db.query(PersonStatus)
                    .filter(PersonStatus.person_id == single_staff["person"]["id"])
                    .first()
                )
                if person_status:
                    students[i]["on_campus"] = person_status.on_campus

                else:
                    person_status = PersonStatus(
                        person_id=single_staff["person"]["id"], on_campus=True
                    )
                    db.add(person_status)
                    db.commit()
                    students[i]["on_campus"] = True
            return students
        except httpx.HTTPStatusError as e:
            return {
                "error": f"HTTP error: {e.response.status_code} - {e.response.text}",
                "resp_content": e.response.text
            }
        except Exception as e:
            return {
                "error": f"Unexpected error: {str(e)}",
                "resp_content": getattr(resp, "text", None)
            }


@router.get("/{student_id}/")
async def get_student_by_id(student_id: int, db: Session = Depends(get_db)):
    # 1) Получаем student из Demographic Service
    async with httpx.AsyncClient(timeout=None) as client:
        response = await client.get(
            f"http://{DEMOGRAPHIC_SERVICE_URL}/students/{student_id}/"
        )

    if response.status_code != 200:
        return Response(
            content=response.content,
            status_code=response.status_code,
            headers=dict(response.headers),
        )

    student = response.json()

    # 2) Добавляем on_campus
    ps = db.query(PersonStatus).filter_by(person_id=student["person"]["id"]).first()
    if ps:
        student["on_campus"] = ps.on_campus
    else:
        ps = PersonStatus(person_id=student["person"]["id"], on_campus=True)
        db.add(ps)
        db.commit()
        student["on_campus"] = True

    return student


@router.delete("/{student_id}/", status_code=status.HTTP_200_OK)
async def delete_student(student_id: int, db: Session = Depends(get_db)):
    """
    1) DELETE /students/{id}/ в Demographic Service (удаляет student + person)
    2) DELETE /auth/user/{person_id} в Auth Service
    3) Удаление записей PersonStatus и SecurityPerson в Security Service
    4) DELETE /person/{person_id} в FaceRecognition Service
    """
    # 1) Демографический сервис
    try:
        async with httpx.AsyncClient() as client:
            resp = await client.delete(
                f"http://{DEMOGRAPHIC_SERVICE_URL}/students/{student_id}/"
            )
    except httpx.RequestError as exc:
        raise HTTPException(status_code=502, detail=f"Demographic unreachable: {exc}")
    if resp.status_code not in (200, 204):
        raise HTTPException(
            status_code=resp.status_code, detail=f"Demo error: {resp.text}"
        )

    data = resp.json() if resp.content else {}
    person_id = data.get("person", {}).get("id")
    if not person_id:
        raise HTTPException(
            status_code=500, detail="Missing person_id in demo response"
        )

    # 2) Auth Service
    try:
        async with httpx.AsyncClient() as client:
            auth_resp = await client.delete(
                f"http://{AUTH_SERVICE_URL}/auth/user/{person_id}"
            )
    except httpx.RequestError as exc:
        raise HTTPException(status_code=502, detail=f"Auth unreachable: {exc}")
    if auth_resp.status_code not in (200, 404):
        raise HTTPException(
            status_code=auth_resp.status_code, detail=f"Auth error: {auth_resp.text}"
        )

    # 3) Security DB cleanup
    ps = db.query(PersonStatus).filter_by(person_id=person_id).first()
    if ps:
        db.delete(ps)
    for sp in db.query(SecurityPerson).filter_by(person_id=person_id).all():
        db.delete(sp)
    db.commit()

    # 4) FaceRecognition Service
    try:
        async with httpx.AsyncClient() as client:
            fr = await client.delete(
                f"http://{FACE_RECOGNITION_SERVICE_URL}/person/{person_id}"
            )
    except httpx.RequestError:
        fr = None
    if fr and fr.status_code not in (200, 204, 404):
        raise HTTPException(status_code=fr.status_code, detail=f"FR error: {fr.text}")
    return {"message": "Student deleted successfully"}
