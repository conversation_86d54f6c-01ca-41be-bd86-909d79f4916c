from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from security_service.dependencies.db import get_db
from security_service.app.services.permission_service import (
    get_permissions,
    update_permission,
)
from security_service.app.schemas.permission import PermissionCreate, PermissionUpdate

router = APIRouter(prefix="/permissions")


@router.put("/{permission_id}/")
async def update_permission_endpoint(
    permission_id: int, permission_data: PermissionUpdate, db: Session = Depends(get_db)
):
    try:
        return update_permission(db, permission_id, permission_data.is_active)
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))


@router.get("/{role_id}/")
async def get_permissions_by_role_id(role_id: int, db: Session = Depends(get_db)):

    return get_permissions(db, role_id)
