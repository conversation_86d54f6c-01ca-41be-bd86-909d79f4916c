from fastapi import APIRouter, Depends
from sqlalchemy.orm import Session
from security_service.dependencies.db import get_db
from security_service.app.models.person_status import PersonStatus
from typing import List
from pydantic import BaseModel


class CampusStatsResponse(BaseModel):
    total_people: int
    on_campus_percentage: float
    off_campus_percentage: float
    on_campus_person_ids: List[int]
    off_campus_person_ids: List[int]


router = APIRouter(prefix="/stats", tags=["Statistics"])


@router.get("/campus-status/", response_model=CampusStatsResponse)
def get_campus_status_stats(db: Session = Depends(get_db)):
    # Get all person statuses
    all_statuses = db.query(PersonStatus).all()
    
    # Calculate total number of people
    total_people = len(all_statuses)
    
    # Separate people by campus status
    on_campus = [status.person_id for status in all_statuses if status.on_campus]
    off_campus = [status.person_id for status in all_statuses if not status.on_campus]
    
    # Calculate percentages
    on_campus_percentage = (len(on_campus) / total_people * 100) if total_people > 0 else 0
    off_campus_percentage = (len(off_campus) / total_people * 100) if total_people > 0 else 0
    
    return CampusStatsResponse(
        total_people=total_people,
        on_campus_percentage=round(on_campus_percentage, 2),
        off_campus_percentage=round(off_campus_percentage, 2),
        on_campus_person_ids=on_campus,
        off_campus_person_ids=off_campus
    )
