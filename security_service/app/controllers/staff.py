import httpx
import json
from fastapi import <PERSON><PERSON><PERSON><PERSON>, Header, HTTPEx<PERSON>, Depends, Request, Response
from shared.service_manager import get_services
from security_service.dependencies.db import get_db
from sqlalchemy.orm import Session
from security_service.app.models.person_status import PersonStatus
from security_service.app.models.security_person import <PERSON><PERSON><PERSON>
from security_service.config import (
    DEMOGRAPHIC_SERVICE_URL,
    AUTH_SERVICE_URL,
    FACE_RECOGNITION_SERVICE_URL,
)
from fastapi.responses import StreamingResponse

router = APIRouter(prefix="/staff")

try:
    services = get_services()
except:
    raise HTTPException("services.json couldn't be loaded")

DEMOGRAPHIC_SERVICE_URL = services["demographic"]
AUTH_SERVICE_URL = services["auth"]


@router.post("/")
async def create_staff(staff_data: dict, db: Session = Depends(get_db)):
    # 1. Создаем студента в Demographic Service
    async with httpx.AsyncClient() as client:
        demographic_response = await client.post(
            f"{DEMOGRAPHIC_SERVICE_URL}/staff/", json=staff_data
        )

    if demographic_response.status_code != 201:
        return Response(
            content=demographic_response.content,
            status_code=demographic_response.status_code,
            headers=dict(demographic_response.headers),
        )
    staff_info = demographic_response.json()
    person_status = PersonStatus(person_id=staff_info["person"]["id"], on_campus=False)
    if "on_campus" in staff_data:
        person_status.on_campus = staff_data["on_campus"]

    db.add(person_status)
    db.commit()

    if "user" in staff_data:
        user = staff_data["user"]
        del staff_data["user"]

        # Получаем идентификатор персоны, созданной в Demographic Service
        person_id = staff_info["person"]["id"]

        # 2. Регистрируем пользователя в Auth Service через /auth/register
        # Обратите внимание, что в Auth Service ожидается ключ "person", а не "person_id"
        user_data = {
            "username": user["username"],
            "password": user["password"],
            "person": person_id,
        }

        async with httpx.AsyncClient() as client:
            auth_response = await client.post(
                f"{AUTH_SERVICE_URL}/auth/register", json=user_data
            )

        if auth_response.status_code not in (200, 201):
            error_details = json.loads(auth_response.text)
            raise HTTPException(
                status_code=auth_response.status_code,
                detail=error_details,
            )
        return {**staff_info, **staff_data, "user": user}

    return {**staff_info, **staff_data}


@router.get("/")
async def get_staff(request: Request, db: Session = Depends(get_db)):

    async with httpx.AsyncClient(timeout=None) as client:
        response = await client.get(
            f"{DEMOGRAPHIC_SERVICE_URL}/staff/", params=request.query_params
        )
        staff = response.json()
    for i, single_staff in enumerate(staff):
        person_status = (
            db.query(PersonStatus)
            .filter(PersonStatus.person_id == single_staff["person"]["id"])
            .first()
        )
        if person_status:
            staff[i]["on_campus"] = person_status.on_campus

        else:
            person_status = PersonStatus(
                person_id=single_staff["person"]["id"], on_campus=True
            )
            db.add(person_status)
            db.commit()
            staff[i]["on_campus"] = True

    return staff


@router.get("/export/")
async def get_staff_excel(request: Request):
    data = get_services()
    try:
        async with httpx.AsyncClient(timeout=60.0) as client:
            response = await client.get(
                f"http://{data['demographic']}/staff/export/", 
                params=request.query_params,
                follow_redirects=True
            )
            response.raise_for_status()
            
            return StreamingResponse(
                response.aiter_bytes(),
                media_type=response.headers.get(
                    "content-type", 
                    "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
                ),
                headers={
                    "Content-Disposition": response.headers.get(
                        "content-disposition", 
                        "attachment; filename=staff.xlsx"
                    )
                }
            )
    except httpx.TimeoutException:
        raise HTTPException(
            status_code=504,
            detail="The request to the demographic service timed out"
        )
    except httpx.HTTPStatusError as e:
        raise HTTPException(
            status_code=e.response.status_code,
            detail=f"Error from demographic service: {e.response.text}"
        )
    except httpx.RequestError as e:
        raise HTTPException(
            status_code=502,
            detail=f"Error connecting to demographic service: {str(e)}"
        )


@router.get("/{staff_id}/")
async def get_staff_by_id(
    staff_id: int,
    db: Session = Depends(get_db),
):
    # Fetch staff from Demographic Service
    async with httpx.AsyncClient(timeout=None) as client:
        response = await client.get(f"{DEMOGRAPHIC_SERVICE_URL}/staff/{staff_id}/")

    if response.status_code != 200:
        return Response(
            content=response.content,
            status_code=response.status_code,
            headers=dict(response.headers),
        )

    staff = response.json()

    # Fetch person's status from local DB
    person_status = (
        db.query(PersonStatus)
        .filter(PersonStatus.person_id == staff["person"]["id"])
        .first()
    )

    if person_status:
        staff["on_campus"] = person_status.on_campus
    else:
        # If not found, assume True and save
        person_status = PersonStatus(person_id=staff["person"]["id"], on_campus=True)
        db.add(person_status)
        db.commit()
        staff["on_campus"] = True

    return staff


from fastapi import APIRouter, Depends, HTTPException, status
from security_service.app.models.security_person import SecurityPerson
from security_service.config import (
    DEMOGRAPHIC_SERVICE_URL,
    AUTH_SERVICE_URL,
    FACE_RECOGNITION_SERVICE_URL,
)


@router.delete("/{staff_id}/", status_code=status.HTTP_200_OK)
async def delete_staff(staff_id: int, db: Session = Depends(get_db)):
    """
    То же самое для Staff:
    1) DELETE /staff/{id}/ на Demographic
    2) DELETE /auth/user/{person_id}
    3) удаление своих записей
    4) DELETE в FaceRecognition
    """
    # 1) Demographic
    try:
        async with httpx.AsyncClient() as client:
            resp = await client.delete(f"{DEMOGRAPHIC_SERVICE_URL}/staff/{staff_id}/")
    except httpx.RequestError as exc:
        raise HTTPException(status_code=502, detail=f"Demographic unreachable: {exc}")
    if resp.status_code not in (200, 204):
        raise HTTPException(
            status_code=resp.status_code, detail=f"Demo error: {resp.text}"
        )

    payload = resp.json() if resp.content else {}
    person_id = payload.get("person", {}).get("id")
    if not person_id:
        raise HTTPException(
            status_code=500, detail="Missing person_id in demo response"
        )

    # 2) Auth
    try:
        async with httpx.AsyncClient() as client:
            auth_resp = await client.delete(f"{AUTH_SERVICE_URL}/auth/user/{person_id}")
    except httpx.RequestError as exc:
        raise HTTPException(status_code=502, detail=f"Auth unreachable: {exc}")
    if auth_resp.status_code not in (200, 404):
        raise HTTPException(
            status_code=auth_resp.status_code, detail=f"Auth error: {auth_resp.text}"
        )

    # 3) Security DB cleanup
    ps = db.query(PersonStatus).filter(PersonStatus.person_id == person_id).first()
    if ps:
        db.delete(ps)
    for sp in (
        db.query(SecurityPerson).filter(SecurityPerson.person_id == person_id).all()
    ):
        db.delete(sp)
    db.commit()

    # 4) FaceRecognition
    try:
        async with httpx.AsyncClient() as client:
            fr = await client.delete(
                f"http://{FACE_RECOGNITION_SERVICE_URL}/person/{person_id}"
            )
    except httpx.RequestError:
        fr = None
    if fr and fr.status_code not in (200, 204, 404):
        raise HTTPException(status_code=fr.status_code, detail=f"FR error: {fr.text}")

    return {"message": "Staff deleted successfully"}
