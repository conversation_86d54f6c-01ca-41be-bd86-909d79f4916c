from sqlalchemy.orm import Session
from security_service.app.models.record import Record
from security_service.app.models.security_person import Security<PERSON>erson
from shared.service_manager import get_services
from shared.get_person_by_id import get_person_by_id
from security_service.app.models.person_status import PersonStatus
import httpx


async def create_record(
    db: Session, security_person_id: int, action: str, method: str, person_id: int
):
    security_person = (
        db.query(SecurityPerson).filter(SecurityPerson.id == security_person_id).first()
    )
    if not security_person:
        raise ValueError("Security Person not found")
    person = await get_person_by_id(person_id)
    if not person:
        raise ValueError(f"Person with {person_id} is not found")
    record = Record(
        security_person_id=security_person_id,
        action=action,
        method=method,
        person_id=person_id,
    )
    person_status = (
        db.query(PersonStatus).filter(PersonStatus.person_id == person_id).first()
    )
    if not person_status:
        person_status = PersonStatus(person_id=person_id, on_campus=True)

    if record.action == "in":
        if person_status.on_campus:
            extra_record = Record(
                security_person_id=record.security_person_id,
                action="out",
                method=record.method,
                person_id=record.person_id,
            )

            db.add(extra_record)
        person_status.on_campus = True
    elif record.action == "out":
        if not person_status.on_campus:
            extra_record = Record(
                security_person_id=record.security_person_id,
                action="in",
                method=record.method,
                person_id=record.person_id,
            )
            db.add(extra_record)
        person_status.on_campus = False
    else:
        raise ValueError(
            f"There is only in and out actions: you provided {record.action}"
        )
    db.add(record)
    db.commit()
    db.refresh(record)
    return record


def delete_record(db: Session, record_id: int):
    record = db.query(Record).filter(Record.id == record_id).first()
    if record:
        db.delete(record)
        db.commit()
    return {"detail": "Record deleted successfully"}


def get_records_by_action(db: Session, action: str, method: str = None):
    query = db.query(Record).filter(Record.action == action)
    if method:
        query = query.filter(Record.method == method)
    return query.all()


async def get_records(
    db: Session, method: str = None, person_id: int = 0, entity: str = None, skip: int = 0, limit: int = 10
):
    query = db.query(Record)
    if method:
        query = query.filter(Record.method == method)
    if person_id:
        query = query.filter(Record.person_id == person_id)

    if entity and entity.lower() in ["staff", "student"]:
        try:
            data = get_services()
        except Exception as e:
            raise Exception(f"Failed to load services configuration: {str(e)}")
            
        if entity.lower() == "staff":
            url = f"http://{data['demographic']}/staff/"
        elif entity.lower() == "student":
            url = f"http://{data['demographic']}/students/"
        else:
            raise Exception(f"Invalid entity type: {entity}")

        async with httpx.AsyncClient() as client:
            response = await client.get(url)
            if response.status_code != 200:
                raise Exception(
                    f"Failed to fetch data from {url}, status code: {response.status_code}"
                )
            try:
                person_ids = [entry["person"]["id"] for entry in response.json()]
            except Exception as e:
                raise Exception(f"Error parsing JSON response: {str(e)}")

        query = query.filter(Record.person_id.in_(person_ids))

    # Apply pagination
    query = query.offset(skip).limit(limit)
    return query.all()
