from sqlalchemy.orm import Session
from security_service.app.models.security_person import <PERSON><PERSON><PERSON>
from security_service.app.models.record import Record

def create_security_person(db: Session, role: str, person_id: int):
    security_person = SecurityPerson(role=role, person_id=person_id)
    db.add(security_person)
    db.commit()
    db.refresh(security_person)
    return security_person

def get_security_person(db: Session, security_person_id: int):
    return db.query(SecurityPerson).filter(SecurityPerson.id == security_person_id).first()

def get_security_person_by_person_id(db: Session, person_id: int):
    return db.query(SecurityPerson).filter(SecurityPerson.person_id == person_id).first()

def update_security_person(db: Session, security_person_id: int, role: str, person_id: int):
    security_person = db.query(SecurityPerson).filter(SecurityPerson.id == security_person_id).first()
    if security_person:
        security_person.role = role
        security_person.person_id = person_id
        db.commit()
        db.refresh(security_person)
    return security_person

def delete_security_person(db: Session, security_person_id: int):
    security_person = db.query(SecurityPerson).filter(SecurityPerson.id == security_person_id).first()
    if security_person:
        # Delete associated records
        db.query(Record).filter(Record.security_person_id == security_person_id).delete(synchronize_session=False)
        db.delete(security_person)
        db.commit()
    return {"detail": "Security Person deleted successfully"}