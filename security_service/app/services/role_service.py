from fastapi import HTTPException, status
from sqlalchemy.orm import Session 
from security_service.app.models.permission import Permission
from security_service.app.models.role import Role
from security_service.app.models.api import Api


def create_role(db: Session, name: str):
    role = db.query(Role).filter(Role.name == name).first()
    if role:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="role with the same name already exists",
        )

    role = Role(name=name)
    db.add(role)
    db.commit()
    db.refresh(role)
    apis = db.query(Api).all()
    for api in apis:
        # Check if the permission already exists
        exists = db.query(Permission).filter_by(api_id=api.id, role_id=role.id).first()
        if not exists:
            permission = Permission(api_id=api.id, role_id=role.id, is_active=False)
            db.add(permission)
    db.commit()
    return role


def get_role(db: Session, role_id: int):
    return db.query(Role).filter(Role.id == role_id).first()


def update_role(db: Session, role_id: int, name: str):
    role = db.query(Role).filter(Role.name == name).first()
    if role:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="role with the same name already exists",
        )
    role = db.query(Role).filter(Role.id == role_id).first()
    if role:
        role.name = name
        db.commit()
        db.refresh(role)
    return role


def get_roles(db: Session):
    roles = db.query(Role).all()
    return roles


def delete_role(db: Session, role_id: int):
    role = db.query(Role).filter(Role.id == role_id).first()
    permissions = db.query(Permission).filter(Permission.role_id == role_id).all()
    for permission in permissions:
        db.delete(permission)
    db.commit()
    if role:
        db.delete(role)
        db.commit()

    return role
