"""
Proxy service for handling request forwarding with permission checks.
"""
import re
from typing import Op<PERSON>, <PERSON><PERSON>
import httpx
from fastapi import HTT<PERSON>Ex<PERSON>, Request, Response, status
from fastapi.routing import compile_path
from sqlalchemy.orm import Session

from security_service.app.models.api import Api
from security_service.app.models.permission import Permission
from security_service.app.models.role import Role
from security_service.app.models.security_person import Security<PERSON>erson
from shared.service_manager import get_services


class ProxyService:
    """Service for handling authenticated proxy requests."""
    
    def __init__(self, db: Session):
        self.db = db
    
    async def forward_request(
        self, 
        path: str, 
        request: Request, 
        person_id: int
    ) -> Response:
        """
        Forward request after authentication and authorization checks.
        
        Args:
            path: The target path to forward to
            request: The original FastAPI request
            person_id: ID of the person making the request
            
        Returns:
            Response from the target service
            
        Raises:
            HTTPException: If authentication, authorization, or forwarding fails
        """
        # Step 1: Authenticate and get user role
        role = await self._authenticate_and_get_role(person_id)
        
        # Step 2: Parse and validate the target path
        service, normalized_path = self._parse_path(path)
        
        # Step 3: Check permissions
        await self._check_permissions(service, normalized_path, role)
        
        # Step 4: Forward the request
        return await self._forward_to_service(service, path, request)
    
    async def _authenticate_and_get_role(self, person_id: int) -> Role:
        """Authenticate user and return their role."""
        security_person = (
            self.db.query(SecurityPerson)
            .filter(SecurityPerson.person_id == person_id)
            .first()
        )
        if not security_person:
            raise HTTPException(
                status_code=404, 
                detail="Security person not found"
            )
        
        role = (
            self.db.query(Role)
            .filter(Role.name == security_person.role)
            .first()
        )
        if not role:
            raise HTTPException(
                status_code=404, 
                detail="Role not found"
            )
        
        return role
    
    def _parse_path(self, path: str) -> Tuple[str, str]:
        """Parse the path and return service name and normalized path."""
        path_parts = path.split("/")
        service = path_parts[0]
        path_remainder = "/".join(path_parts[1:])
        normalized_path = f"/{path_remainder}".rstrip("/")
        
        return service, normalized_path
    
    async def _check_permissions(
        self, 
        service: str, 
        normalized_path: str, 
        role: Role
    ) -> None:
        """Check if the user has permission to access the path."""
        # Find matching API
        matched_api = await self._find_matching_api(service, normalized_path)
        if not matched_api:
            raise HTTPException(
                status_code=404, 
                detail=f"API not found: {service}{normalized_path}"
            )
        
        # Check permission
        permission = (
            self.db.query(Permission)
            .filter(
                Permission.api_id == matched_api.id,
                Permission.role_id == role.id,
                Permission.is_active == True,
            )
            .first()
        )
        
        if not permission:
            raise HTTPException(
                status_code=403,
                detail=f"Permission denied for role {role.name} and path {normalized_path}",
            )
    
    async def _find_matching_api(
        self, 
        service: str, 
        normalized_path: str
    ) -> Optional[Api]:
        """Find API that matches the service and path."""
        apis = self.db.query(Api).filter(Api.service_name == service).all()
        
        for api in apis:
            # Compile the stored API path into a regex pattern
            regex, _, _ = compile_path(api.api_path.rstrip("/"))
            if re.fullmatch(regex, normalized_path):
                return api
        
        return None
    
    async def _forward_to_service(
        self, 
        service: str, 
        path: str, 
        request: Request
    ) -> Response:
        """Forward the request to the target service."""
        # Get service configuration
        try:
            services_config = get_services()
            target_service = services_config[service]
        except Exception:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Service configuration error: {service} not found",
            )
        
        # Build target URL
        path_remainder = "/".join(path.split("/")[1:])
        path_remainder = path_remainder.rstrip("/") + "/"
        target_url = f"http://{target_service}/{path_remainder}"
        
        # Add query parameters
        if request.query_params:
            query_string = "&".join(
                f"{key}={value}" for key, value in request.query_params.items()
            )
            target_url += f"?{query_string}"
        
        # Forward request
        headers = dict(request.headers)
        body = await request.body()
        
        async with httpx.AsyncClient(timeout=60.0) as client:
            try:
                response = await client.request(
                    method=request.method,
                    url=target_url,
                    headers=headers,
                    content=body,
                )
                
                return Response(
                    content=response.content,
                    status_code=response.status_code,
                    headers=dict(response.headers),
                )
                
            except httpx.RequestError as exc:
                raise HTTPException(
                    status_code=502,
                    detail=f"Error connecting to target service: {exc}",
                )
