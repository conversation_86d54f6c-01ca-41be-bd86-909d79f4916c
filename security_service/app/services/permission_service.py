from sqlalchemy.orm import Session, joinedload
from security_service.app.models.permission import Permission
from security_service.app.models.api import Api
from security_service.app.models.role import Role


def get_permissions(db: Session, role_id: int):
    return (
        db.query(Permission)
        .options(joinedload(Permission.api))
        .filter(Permission.role_id == role_id)
        .all()
    )


def update_permission(db: Session, permission_id: int, is_active: bool):
    permission = db.query(Permission).filter(Permission.id == permission_id).first()
    if not permission:
        raise ValueError("Permission not found")

    permission.is_active = is_active
    db.commit()
    db.refresh(permission)

    return permission
