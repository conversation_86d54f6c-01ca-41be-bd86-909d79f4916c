from sqlalchemy import Column, Integer, String, DateTime, ForeignKey
from sqlalchemy.orm import relationship
from datetime import datetime
from security_service.dependencies.db import Base

class Record(Base):
    __tablename__ = "record"
    
    id = Column(Integer, primary_key=True, index=True)
    security_person_id = Column(Integer, ForeignKey("security_person.id"), nullable=False)
    action = Column(String, nullable=False)
    
    timestamp = Column(DateTime, default=datetime.utcnow, nullable=False)
    method = Column(String, nullable=False)
    person_id = Column(Integer, nullable=False)

    security_person = relationship("SecurityPerson", back_populates="records")