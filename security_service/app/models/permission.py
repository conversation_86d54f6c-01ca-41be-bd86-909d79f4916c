from sqlalchemy import Column, Integer, DateTime, ForeignKey, Boolean
from sqlalchemy.orm import relationship
from datetime import datetime
from security_service.dependencies.db import Base

class Permission(Base):
    __tablename__ = "permission"
    
    id = Column(Integer, primary_key=True, index=True)
    api_id = Column(Integer, ForeignKey("api.id"), nullable=False)
    role_id = Column(Integer, ForeignKey("role.id"), nullable=False)
    time_created = Column(DateTime, default=datetime.utcnow, nullable=False)
    is_active = Column(Boolean, default=True, nullable=False)
    
    api = relationship("Api", back_populates="permissions")
    role = relationship("Role", back_populates="permissions")
