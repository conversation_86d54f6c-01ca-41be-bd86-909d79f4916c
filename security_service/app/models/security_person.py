from sqlalchemy import Column, Integer, String, DateTime
from sqlalchemy.orm import relationship
from datetime import datetime
from security_service.dependencies.db import Base
from sqlalchemy import CheckConstraint


VALID_ROLES = ["superadmin", "admin", "security"]


class SecurityPerson(Base):
    __tablename__ = "security_person"
    
    id = Column(Integer, primary_key=True, index=True)
    role = Column(String, nullable=False)
    person_id = Column(Integer, nullable=False)
    time_created = Column(DateTime, default=datetime.utcnow, nullable=False)
    
    records = relationship("Record", back_populates="security_person")

    __table_args__ = (
        CheckConstraint(
            f"role IN {tuple(VALID_ROLES)}",
            name="valid_role_check"
        ),
    )