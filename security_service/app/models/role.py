from sqlalchemy import Column, Integer, String, DateTime
from security_service.dependencies.db import Base
from sqlalchemy.orm import relationship
from datetime import datetime


class Role(Base):
    __tablename__ = "role"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String, nullable=False, unique=True)
    time_created = Column(DateTime, default=datetime.utcnow, nullable=False)

    permissions = relationship("Permission", back_populates="role")
