from sqlalchemy import Column, Integer, String, DateTime
from sqlalchemy.orm import relationship
from datetime import datetime
from security_service.dependencies.db import Base


class Api(Base):
    __tablename__ = "api"
    
    id = Column(Integer, primary_key=True, index=True)
    service_name = Column(String, nullable=False)
    api_path = Column(String, nullable=False)
    method = Column(String, nullable=False)
    time_created = Column(DateTime, default=datetime.utcnow, nullable=False)
    
    permissions = relationship("Permission", back_populates="api")