from sqlalchemy import Column, Integer, String, DateTime, ForeignKey, Boolean
from sqlalchemy.orm import relationship
from datetime import datetime
from security_service.dependencies.db import Base


class PersonStatus(Base):
    __tablename__ = "person_status"

    id = Column(Integer, primary_key=True, index=True)
    person_id = Column(Integer, nullable=False, unique=True)
    on_campus = Column(Boolean, nullable=False)
