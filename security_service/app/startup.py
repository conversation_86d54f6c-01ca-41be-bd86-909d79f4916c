"""
Application startup and initialization logic.
"""

import logging
from typing import Dict, Any

from auth_service.main import app as auth_app
from demographic_service.main import app as demographic_app
from scripts.create_apis import create_apis
from scripts.create_permissions import create_permissions
from scripts.create_roles import create_roles
from scripts.init_first_user import init_first_user
from scripts.load_staff import load_staff


logger = logging.getLogger(__name__)


async def initialize_application_data() -> None:
    """
    Initialize application data on startup.

    This function sets up:
    - API definitions
    - Roles and permissions
    - Default superuser
    - Staff data

    Raises:
        Exception: If initialization fails
    """
    try:
        await _initialize_apis()
        await _initialize_roles()
        await _initialize_permissions()
        await _initialize_default_user()
        await _initialize_staff_data()

        logger.info("Application initialization completed successfully")

    except Exception as e:
        logger.error(f"Application initialization failed: {e}")
        raise


async def _initialize_apis() -> None:
    """Initialize API definitions."""
    logger.info("Initializing APIs...")

    # Import here to avoid circular imports
    # We'll get the security app from the factory instead
    from security_service.app.factory import create_app

    security_app = create_app()

    apps_config: Dict[str, Any] = {
        "Security": security_app,
        "Auth": auth_app,
        "Demographic": demographic_app,
    }

    create_apis(apps_config)
    logger.info("APIs initialized successfully")


async def _initialize_roles() -> None:
    """Initialize system roles."""
    logger.info("Initializing roles...")
    create_roles()
    logger.info("Roles initialized successfully")


async def _initialize_permissions() -> None:
    """Initialize permissions."""
    logger.info("Initializing permissions...")
    create_permissions()
    logger.info("Permissions initialized successfully")


async def _initialize_default_user() -> None:
    """Initialize default superuser."""
    logger.info("Creating default superuser...")
    init_first_user()
    logger.info("Default superuser created successfully")


async def _initialize_staff_data() -> None:
    """Initialize staff data."""
    logger.info("Loading staff data...")
    load_staff()
    logger.info("Staff data loaded successfully")
