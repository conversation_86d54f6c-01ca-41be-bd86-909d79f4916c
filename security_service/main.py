# Standard library imports
import re

# Third-party imports
import httpx  # type: ignore
from fastapi import <PERSON><PERSON><PERSON>, Depends, HTTPException, Header, Response, Request, status
from fastapi.middleware.cors import CORSMiddleware
from fastapi.routing import compile_path
from sqlalchemy.orm import Session

# Local application imports
from security_service.app.models.api import Api
from security_service.app.models.permission import Permission
from security_service.app.models.person_status import PersonStatus
from security_service.app.models.record import (
    Record,
)  # Don't delete - needed for startup
from security_service.app.models.role import Role
from security_service.app.models.security_person import SecurityPerson
from security_service.dependencies.db import get_db

# Router imports
from security_service.app.controllers import (
    role_router,
    permission_router,
    record_router,
    security_person_router,
    face_recognition_router,
    staff_router,
    student_router,
    stats_router,
)
from security_service.app.v1.me import router as me_router

# External service imports
from auth_service.main import app as auth_app
from demographic_service.main import app as demographic_app
from shared.service_manager import get_services

# Script imports
from scripts.create_apis import create_apis
from scripts.create_permissions import create_permissions
from scripts.create_roles import create_roles
from scripts.init_first_user import init_first_user
from scripts.load_staff import load_staff

app = FastAPI()

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Allow all origins
    allow_credentials=True,
    allow_methods=["*"],  # Allow all HTTP methods
    allow_headers=["*"],  # Allow all headers
)


# Register API routers
app.include_router(student_router)
app.include_router(staff_router)
app.include_router(role_router)
app.include_router(permission_router)
app.include_router(record_router)
app.include_router(security_person_router)
app.include_router(face_recognition_router)
app.include_router(stats_router)
app.include_router(me_router)


@app.api_route(
    "/srv/{path:path}",
    methods=["GET", "POST", "PUT", "DELETE", "PATCH", "OPTIONS", "HEAD"],
)
async def proxy_request(
    path: str,
    request: Request,
    db: Session = Depends(get_db),
    person_id: int = Header(alias="person_id"),
):
    print(f"Security service: Handling request for path {path}")

    # Step 1: Get the user by person_id
    security_person = (
        db.query(SecurityPerson).filter(SecurityPerson.person_id == person_id).first()
    )
    if not security_person:
        raise HTTPException(status_code=404, detail="Security person not found")

    # Step 2: Get the user's role
    role = db.query(Role).filter(Role.name == security_person.role).first()
    if not role:
        raise HTTPException(status_code=404, detail="Role not found")

    # Step 3: Match the path dynamically
    apis = db.query(Api).all()
    path_parts = path.split("/")
    service = path_parts[0]
    path_remainder = "/".join(path_parts[1:])
    normalized_path = f"/{path_remainder}".rstrip("/")

    print(
        f"Security service: Checking permissions for service={service}, path={normalized_path}"
    )

    matched_api = None
    for api in apis:
        if api.service_name != service:
            continue
        # Compile the stored API path into a regex pattern
        regex, _, _ = compile_path(api.api_path.rstrip("/"))  # Normalize DB path too
        if re.fullmatch(regex, normalized_path):
            matched_api = api
            break

    if not matched_api:
        raise HTTPException(
            status_code=404, detail=f"API not found: {service}{normalized_path}"
        )

    # Step 4: Check for permission with the given path and role
    permission = (
        db.query(Permission)
        .filter(
            Permission.api_id == matched_api.id,
            Permission.role_id == role.id,
            Permission.is_active == True,
        )
        .first()
    )
    if not permission:
        raise HTTPException(
            status_code=403,
            detail=f"Permission denied for role {role.name} and path {normalized_path}",
        )

    # Step 5: Forward the request if permission exists
    path_remainder = path_remainder.rstrip("/") + "/"

    try:
        data = get_services()
        target_service = data[service]
    except Exception:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Service configuration error: {service} not found",
        )

    target_url = f"http://{target_service}/{path_remainder}"
    print(f"Security service: Target URL is {target_url}")

    # Extract query parameters
    query_params = request.query_params
    if query_params:
        target_url += "?" + "&".join(
            f"{key}={value}" for key, value in query_params.items()
        )

    # Set up headers
    headers = dict(request.headers)

    # Check if this is a multipart/form-data request
    content_type = headers.get("content-type", "")

    if content_type.startswith("multipart/form-data"):
        print("Security service: Detected multipart/form-data request")
        # Don't try to parse the form - just forward the raw bytes
        body = await request.body()

        # Forward the raw bytes with the same content-type header
        async with httpx.AsyncClient(timeout=60.0) as client:
            try:
                response = await client.request(
                    method=request.method,
                    url=target_url,
                    headers=headers,
                    content=body,  # Forward the raw body bytes
                )
                print(
                    f"Security service: Forwarded multipart request, got status {response.status_code}"
                )
            except httpx.RequestError as exc:
                print(f"Security service: Error connecting to target service: {exc}")
                raise HTTPException(
                    status_code=502, detail=f"Error connecting to target service: {exc}"
                )
    else:
        # For non-multipart requests, forward the body as usual
        body = await request.body()
        async with httpx.AsyncClient(timeout=60.0) as client:
            try:
                response = await client.request(
                    method=request.method,
                    url=target_url,
                    headers=headers,
                    content=body,
                )
            except httpx.RequestError as exc:
                raise HTTPException(
                    status_code=502, detail=f"Error connecting to target service: {exc}"
                )

    # Return the response
    return Response(
        content=response.content,
        status_code=response.status_code,
        headers=dict(response.headers),
    )


@app.on_event("startup")
async def initialize_data():
    """Run initialization tasks on application startup."""
    try:
        print("Initializing APIs...")
        create_apis({"Security": app, "Auth": auth_app, "Demographic": demographic_app})
        print("APIs created successfully.")

        print("Initializing Roles...")
        create_roles()
        print("Roles created successfully.")

        print("Initializing Permissions...")
        create_permissions()
        print("Permissions created successfully.")

        print("Creating test superuser")
        init_first_user()
        print("Test super user is created")

        print("Loading staff data")
        load_staff()
        print("Loading completed")

    except Exception as e:
        print(f"Error during initialization: {str(e)}")
        raise HTTPException(status_code=500, detail="Initialization failed")
