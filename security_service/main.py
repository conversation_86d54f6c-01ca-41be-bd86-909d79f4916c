"""
Security Service - Main application entry point.

This service handles authentication, authorization, and request proxying
for the microservices architecture.
"""

# Import models to ensure they're registered with SQLAlchemy
from security_service.app.models.api import Api  # noqa: F401
from security_service.app.models.permission import Permission  # noqa: F401
from security_service.app.models.person_status import PersonStatus  # noqa: F401
from security_service.app.models.record import Record  # noqa: F401
from security_service.app.models.role import Role  # noqa: F401
from security_service.app.models.security_person import SecurityPerson  # noqa: F401

# Create the application using the factory pattern
from security_service.app.factory import create_app

app = create_app()
