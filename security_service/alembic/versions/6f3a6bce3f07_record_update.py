"""record update

Revision ID: 6f3a6bce3f07
Revises: 244427546bb7
Create Date: 2025-04-08 16:38:46.521969

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '6f3a6bce3f07'
down_revision = '244427546bb7'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('record', sa.Column('person_id', sa.Integer(), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('record', 'person_id')
    # ### end Alembic commands ###
