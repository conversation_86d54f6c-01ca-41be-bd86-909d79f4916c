"""Initial migration

Revision ID: 244427546bb7
Revises: 
Create Date: 2025-04-06 17:14:19.260110

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '244427546bb7'
down_revision = None
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('api',
    sa.<PERSON>umn('id', sa.Integer(), nullable=False),
    sa.<PERSON>umn('service_name', sa.String(), nullable=False),
    sa.Column('api_path', sa.String(), nullable=False),
    sa.Column('method', sa.String(), nullable=False),
    sa.Column('time_created', sa.DateTime(), nullable=False),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_api_id'), 'api', ['id'], unique=False)
    op.create_table('role',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('name', sa.String(), nullable=False),
    sa.Column('time_created', sa.DateTime(), nullable=False),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_role_id'), 'role', ['id'], unique=False)
    op.create_table('security_person',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('role', sa.String(), nullable=False),
    sa.Column('person_id', sa.Integer(), nullable=False),
    sa.Column('time_created', sa.DateTime(), nullable=False),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_security_person_id'), 'security_person', ['id'], unique=False)
    op.create_table('permission',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('api_id', sa.Integer(), nullable=False),
    sa.Column('role_id', sa.Integer(), nullable=False),
    sa.Column('time_created', sa.DateTime(), nullable=False),
    sa.Column('is_active', sa.Boolean(), nullable=False),
    sa.ForeignKeyConstraint(['api_id'], ['api.id'], ),
    sa.ForeignKeyConstraint(['role_id'], ['role.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_permission_id'), 'permission', ['id'], unique=False)
    op.create_table('record',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('security_person_id', sa.Integer(), nullable=False),
    sa.Column('action', sa.String(), nullable=False),
    sa.Column('timestamp', sa.DateTime(), nullable=False),
    sa.Column('method', sa.String(), nullable=False),
    sa.ForeignKeyConstraint(['security_person_id'], ['security_person.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_record_id'), 'record', ['id'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_record_id'), table_name='record')
    op.drop_table('record')
    op.drop_index(op.f('ix_permission_id'), table_name='permission')
    op.drop_table('permission')
    op.drop_index(op.f('ix_security_person_id'), table_name='security_person')
    op.drop_table('security_person')
    op.drop_index(op.f('ix_role_id'), table_name='role')
    op.drop_table('role')
    op.drop_index(op.f('ix_api_id'), table_name='api')
    op.drop_table('api')
    # ### end Alembic commands ###
