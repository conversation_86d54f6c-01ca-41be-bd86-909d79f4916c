"""empty message

Revision ID: e37173f0664d
Revises: f6f402bbd740
Create Date: 2025-04-28 02:05:16.362169

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'e37173f0664d'
down_revision = 'f6f402bbd740'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_unique_constraint(None, 'role', ['name'])
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(None, 'role', type_='unique')
    # ### end Alembic commands ###
