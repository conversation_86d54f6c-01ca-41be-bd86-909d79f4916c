"""empty message

Revision ID: f6f402bbd740
Revises: 6f3a6bce3f07
Create Date: 2025-04-23 23:20:49.562408

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'f6f402bbd740'
down_revision = '6f3a6bce3f07'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('person_status',
    sa.<PERSON>umn('id', sa.Integer(), nullable=False),
    sa.Column('person_id', sa.Integer(), nullable=False),
    sa.Column('on_campus', sa.<PERSON>(), nullable=False),
    sa.<PERSON>eyConstraint('id'),
    sa.UniqueConstraint('person_id')
    )
    op.create_index(op.f('ix_person_status_id'), 'person_status', ['id'], unique=False)
    op.alter_column('record', 'person_id',
               existing_type=sa.INTEGER(),
               nullable=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('record', 'person_id',
               existing_type=sa.INTEGER(),
               nullable=True)
    op.drop_index(op.f('ix_person_status_id'), table_name='person_status')
    op.drop_table('person_status')
    # ### end Alembic commands ###
