import os
from dotenv import load_dotenv

load_dotenv()

redis_server = os.environ.get("redis_server")
redis_port = os.environ.get("redis_port")

db_user = os.environ.get("SECURITY_SERVICE_DB_USER")
db_password = os.environ.get("SECURITY_SERVICE_DB_PASSWORD")
db_host = os.environ.get("SECURITY_SERVICE_DB_HOST")
db_port = os.environ.get("SECURITY_SERVICE_DB_PORT")
db_name = os.environ.get("SECURITY_SERVICE_DB_NAME")

demographic_db_user = os.environ.get("DEMOGRAPHIC_DATA_DB_USER")
demographic_db_password = os.environ.get("DEMOGRAPHIC_DATA_DB_PASSWORD")
demographic_db_host = os.environ.get("DEMOGRAPHIC_DATA_DB_HOST")
demographic_db_port = os.environ.get("DEMOGRAPHIC_DATA_DB_PORT")
demographic_db_name = os.environ.get("DEMOGRAPHIC_DATA_DB_NAME")

image_sub_folder = os.environ.get("image_sub_folder")
batch_size = os.cpu_count()

skip_interval = 1

import json
from pathlib import Path

# Находим service.json в корне security_service
SERVICE_FILE = Path(__file__).parent.parent / "services.json"
_services = json.loads(SERVICE_FILE.read_text())

# Собираем полные URL
AUTH_SERVICE_URL             = f"http://{_services['auth']}"
DEMOGRAPHIC_SERVICE_URL      = f"http://{_services['demographic']}"
FACE_RECOGNITION_SERVICE_URL = f"http://{_services['recognition']}"