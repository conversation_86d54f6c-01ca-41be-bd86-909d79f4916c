#!/bin/bash

# Wait for database to be ready
until PGPASSWORD=ATS3cwn5bw psql -h auth_db -U postgres -c "SELECT 1" >/dev/null 2>&1; do
  echo "Waiting for database to be ready..."
  sleep 1
done
echo "Database is ready!"

# Run migrations
cd -

echo "Running auth service migrations..."
alembic -c auth_service/alembic.ini upgrade head 

echo "Running demographic service migrations..."
alembic -c demographic_service/alembic.ini upgrade head 

echo "Running security service migrations..."
alembic -c security_service/alembic.ini upgrade head 

# Start services
echo "Starting services..."
uvicorn auth_service.main:app --host 0.0.0.0 --port 8000 &
uvicorn demographic_service.main:app --host 0.0.0.0 --port 8001 &
uvicorn security_service.main:app --host 0.0.0.0 --port 8002 &
wait 